// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemExerciseHistoryBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final LinearLayout contentContainer;

  @NonNull
  public final TextView scoreLabel;

  @NonNull
  public final TextView scoreText;

  @NonNull
  public final TextView timeText;

  @NonNull
  public final TextView titleText;

  private ItemExerciseHistoryBinding(@NonNull MaterialCardView rootView,
      @NonNull LinearLayout contentContainer, @NonNull TextView scoreLabel,
      @NonNull TextView scoreText, @NonNull TextView timeText, @NonNull TextView titleText) {
    this.rootView = rootView;
    this.contentContainer = contentContainer;
    this.scoreLabel = scoreLabel;
    this.scoreText = scoreText;
    this.timeText = timeText;
    this.titleText = titleText;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemExerciseHistoryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemExerciseHistoryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_exercise_history, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemExerciseHistoryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.contentContainer;
      LinearLayout contentContainer = ViewBindings.findChildViewById(rootView, id);
      if (contentContainer == null) {
        break missingId;
      }

      id = R.id.scoreLabel;
      TextView scoreLabel = ViewBindings.findChildViewById(rootView, id);
      if (scoreLabel == null) {
        break missingId;
      }

      id = R.id.scoreText;
      TextView scoreText = ViewBindings.findChildViewById(rootView, id);
      if (scoreText == null) {
        break missingId;
      }

      id = R.id.timeText;
      TextView timeText = ViewBindings.findChildViewById(rootView, id);
      if (timeText == null) {
        break missingId;
      }

      id = R.id.titleText;
      TextView titleText = ViewBindings.findChildViewById(rootView, id);
      if (titleText == null) {
        break missingId;
      }

      return new ItemExerciseHistoryBinding((MaterialCardView) rootView, contentContainer,
          scoreLabel, scoreText, timeText, titleText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
