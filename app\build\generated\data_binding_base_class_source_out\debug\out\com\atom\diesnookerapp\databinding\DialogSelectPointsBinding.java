// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.NumberPicker;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogSelectPointsBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton cancelButton;

  @NonNull
  public final TextView dialogTitle;

  @NonNull
  public final NumberPicker pointsPicker;

  @NonNull
  public final LinearLayout pointsPickerLayout;

  @NonNull
  public final MaterialButton saveButton;

  @NonNull
  public final TextView selectedPointsText;

  @NonNull
  public final TextView taskTitleText;

  private DialogSelectPointsBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton cancelButton, @NonNull TextView dialogTitle,
      @NonNull NumberPicker pointsPicker, @NonNull LinearLayout pointsPickerLayout,
      @NonNull MaterialButton saveButton, @NonNull TextView selectedPointsText,
      @NonNull TextView taskTitleText) {
    this.rootView = rootView;
    this.cancelButton = cancelButton;
    this.dialogTitle = dialogTitle;
    this.pointsPicker = pointsPicker;
    this.pointsPickerLayout = pointsPickerLayout;
    this.saveButton = saveButton;
    this.selectedPointsText = selectedPointsText;
    this.taskTitleText = taskTitleText;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogSelectPointsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogSelectPointsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_select_points, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogSelectPointsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cancelButton;
      MaterialButton cancelButton = ViewBindings.findChildViewById(rootView, id);
      if (cancelButton == null) {
        break missingId;
      }

      id = R.id.dialogTitle;
      TextView dialogTitle = ViewBindings.findChildViewById(rootView, id);
      if (dialogTitle == null) {
        break missingId;
      }

      id = R.id.pointsPicker;
      NumberPicker pointsPicker = ViewBindings.findChildViewById(rootView, id);
      if (pointsPicker == null) {
        break missingId;
      }

      id = R.id.pointsPickerLayout;
      LinearLayout pointsPickerLayout = ViewBindings.findChildViewById(rootView, id);
      if (pointsPickerLayout == null) {
        break missingId;
      }

      id = R.id.saveButton;
      MaterialButton saveButton = ViewBindings.findChildViewById(rootView, id);
      if (saveButton == null) {
        break missingId;
      }

      id = R.id.selectedPointsText;
      TextView selectedPointsText = ViewBindings.findChildViewById(rootView, id);
      if (selectedPointsText == null) {
        break missingId;
      }

      id = R.id.taskTitleText;
      TextView taskTitleText = ViewBindings.findChildViewById(rootView, id);
      if (taskTitleText == null) {
        break missingId;
      }

      return new DialogSelectPointsBinding((ConstraintLayout) rootView, cancelButton, dialogTitle,
          pointsPicker, pointsPickerLayout, saveButton, selectedPointsText, taskTitleText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
