// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentExerciseDetailBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton addScoreButton;

  @NonNull
  public final MaterialButton addTimeButton;

  @NonNull
  public final TextView lastScoreText;

  @NonNull
  public final MaterialButton showGraphButton;

  @NonNull
  public final TextView titleText;

  private FragmentExerciseDetailBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton addScoreButton, @NonNull MaterialButton addTimeButton,
      @NonNull TextView lastScoreText, @NonNull MaterialButton showGraphButton,
      @NonNull TextView titleText) {
    this.rootView = rootView;
    this.addScoreButton = addScoreButton;
    this.addTimeButton = addTimeButton;
    this.lastScoreText = lastScoreText;
    this.showGraphButton = showGraphButton;
    this.titleText = titleText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentExerciseDetailBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentExerciseDetailBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_exercise_detail, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentExerciseDetailBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addScoreButton;
      MaterialButton addScoreButton = ViewBindings.findChildViewById(rootView, id);
      if (addScoreButton == null) {
        break missingId;
      }

      id = R.id.addTimeButton;
      MaterialButton addTimeButton = ViewBindings.findChildViewById(rootView, id);
      if (addTimeButton == null) {
        break missingId;
      }

      id = R.id.lastScoreText;
      TextView lastScoreText = ViewBindings.findChildViewById(rootView, id);
      if (lastScoreText == null) {
        break missingId;
      }

      id = R.id.showGraphButton;
      MaterialButton showGraphButton = ViewBindings.findChildViewById(rootView, id);
      if (showGraphButton == null) {
        break missingId;
      }

      id = R.id.titleText;
      TextView titleText = ViewBindings.findChildViewById(rootView, id);
      if (titleText == null) {
        break missingId;
      }

      return new FragmentExerciseDetailBinding((LinearLayout) rootView, addScoreButton,
          addTimeButton, lastScoreText, showGraphButton, titleText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
