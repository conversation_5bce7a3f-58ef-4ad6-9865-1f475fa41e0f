// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.tabs.TabLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogExerciseSelectionBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton addButton;

  @NonNull
  public final MaterialButton cancelButton;

  @NonNull
  public final TabLayout categoryTabs;

  @NonNull
  public final TextView dialogTitle;

  @NonNull
  public final RecyclerView exercisesRecyclerView;

  private DialogExerciseSelectionBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton addButton, @NonNull MaterialButton cancelButton,
      @NonNull TabLayout categoryTabs, @NonNull TextView dialogTitle,
      @NonNull RecyclerView exercisesRecyclerView) {
    this.rootView = rootView;
    this.addButton = addButton;
    this.cancelButton = cancelButton;
    this.categoryTabs = categoryTabs;
    this.dialogTitle = dialogTitle;
    this.exercisesRecyclerView = exercisesRecyclerView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogExerciseSelectionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogExerciseSelectionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_exercise_selection, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogExerciseSelectionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addButton;
      MaterialButton addButton = ViewBindings.findChildViewById(rootView, id);
      if (addButton == null) {
        break missingId;
      }

      id = R.id.cancelButton;
      MaterialButton cancelButton = ViewBindings.findChildViewById(rootView, id);
      if (cancelButton == null) {
        break missingId;
      }

      id = R.id.categoryTabs;
      TabLayout categoryTabs = ViewBindings.findChildViewById(rootView, id);
      if (categoryTabs == null) {
        break missingId;
      }

      id = R.id.dialogTitle;
      TextView dialogTitle = ViewBindings.findChildViewById(rootView, id);
      if (dialogTitle == null) {
        break missingId;
      }

      id = R.id.exercisesRecyclerView;
      RecyclerView exercisesRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (exercisesRecyclerView == null) {
        break missingId;
      }

      return new DialogExerciseSelectionBinding((ConstraintLayout) rootView, addButton,
          cancelButton, categoryTabs, dialogTitle, exercisesRecyclerView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
