// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemMonthHeaderBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView entriesCountText;

  @NonNull
  public final ImageView expandCollapseIcon;

  @NonNull
  public final LinearLayout headerLayout;

  @NonNull
  public final MaterialCardView monthCardView;

  @NonNull
  public final TextView monthText;

  private ItemMonthHeaderBinding(@NonNull MaterialCardView rootView,
      @NonNull TextView entriesCountText, @NonNull ImageView expandCollapseIcon,
      @NonNull LinearLayout headerLayout, @NonNull MaterialCardView monthCardView,
      @NonNull TextView monthText) {
    this.rootView = rootView;
    this.entriesCountText = entriesCountText;
    this.expandCollapseIcon = expandCollapseIcon;
    this.headerLayout = headerLayout;
    this.monthCardView = monthCardView;
    this.monthText = monthText;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemMonthHeaderBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemMonthHeaderBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_month_header, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemMonthHeaderBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.entriesCountText;
      TextView entriesCountText = ViewBindings.findChildViewById(rootView, id);
      if (entriesCountText == null) {
        break missingId;
      }

      id = R.id.expandCollapseIcon;
      ImageView expandCollapseIcon = ViewBindings.findChildViewById(rootView, id);
      if (expandCollapseIcon == null) {
        break missingId;
      }

      id = R.id.headerLayout;
      LinearLayout headerLayout = ViewBindings.findChildViewById(rootView, id);
      if (headerLayout == null) {
        break missingId;
      }

      MaterialCardView monthCardView = (MaterialCardView) rootView;

      id = R.id.monthText;
      TextView monthText = ViewBindings.findChildViewById(rootView, id);
      if (monthText == null) {
        break missingId;
      }

      return new ItemMonthHeaderBinding((MaterialCardView) rootView, entriesCountText,
          expandCollapseIcon, headerLayout, monthCardView, monthText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
