// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogCustomTimeframeBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton endDateButton;

  @NonNull
  public final MaterialButton startDateButton;

  private DialogCustomTimeframeBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton endDateButton, @NonNull MaterialButton startDateButton) {
    this.rootView = rootView;
    this.endDateButton = endDateButton;
    this.startDateButton = startDateButton;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogCustomTimeframeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogCustomTimeframeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_custom_timeframe, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogCustomTimeframeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.endDateButton;
      MaterialButton endDateButton = ViewBindings.findChildViewById(rootView, id);
      if (endDateButton == null) {
        break missingId;
      }

      id = R.id.startDateButton;
      MaterialButton startDateButton = ViewBindings.findChildViewById(rootView, id);
      if (startDateButton == null) {
        break missingId;
      }

      return new DialogCustomTimeframeBinding((LinearLayout) rootView, endDateButton,
          startDateButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
