// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import com.atom.diesnookerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;

public final class ThemePreviewCrimsonBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final CardView crimsonThemePreview;

  private ThemePreviewCrimsonBinding(@NonNull CardView rootView,
      @NonNull CardView crimsonThemePreview) {
    this.rootView = rootView;
    this.crimsonThemePreview = crimsonThemePreview;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ThemePreviewCrimsonBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ThemePreviewCrimsonBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.theme_preview_crimson, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ThemePreviewCrimsonBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    CardView crimsonThemePreview = (CardView) rootView;

    return new ThemePreviewCrimsonBinding((CardView) rootView, crimsonThemePreview);
  }
}
