// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogCustomExerciseBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton addButton;

  @NonNull
  public final MaterialButton cancelButton;

  @NonNull
  public final TextView dialogTitle;

  @NonNull
  public final TextInputEditText exerciseNameInput;

  @NonNull
  public final TextInputLayout exerciseNameLayout;

  @NonNull
  public final AutoCompleteTextView exerciseTypeDropdown;

  @NonNull
  public final TextInputLayout exerciseTypeLayout;

  private DialogCustomExerciseBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton addButton, @NonNull MaterialButton cancelButton,
      @NonNull TextView dialogTitle, @NonNull TextInputEditText exerciseNameInput,
      @NonNull TextInputLayout exerciseNameLayout,
      @NonNull AutoCompleteTextView exerciseTypeDropdown,
      @NonNull TextInputLayout exerciseTypeLayout) {
    this.rootView = rootView;
    this.addButton = addButton;
    this.cancelButton = cancelButton;
    this.dialogTitle = dialogTitle;
    this.exerciseNameInput = exerciseNameInput;
    this.exerciseNameLayout = exerciseNameLayout;
    this.exerciseTypeDropdown = exerciseTypeDropdown;
    this.exerciseTypeLayout = exerciseTypeLayout;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogCustomExerciseBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogCustomExerciseBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_custom_exercise, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogCustomExerciseBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addButton;
      MaterialButton addButton = ViewBindings.findChildViewById(rootView, id);
      if (addButton == null) {
        break missingId;
      }

      id = R.id.cancelButton;
      MaterialButton cancelButton = ViewBindings.findChildViewById(rootView, id);
      if (cancelButton == null) {
        break missingId;
      }

      id = R.id.dialogTitle;
      TextView dialogTitle = ViewBindings.findChildViewById(rootView, id);
      if (dialogTitle == null) {
        break missingId;
      }

      id = R.id.exerciseNameInput;
      TextInputEditText exerciseNameInput = ViewBindings.findChildViewById(rootView, id);
      if (exerciseNameInput == null) {
        break missingId;
      }

      id = R.id.exerciseNameLayout;
      TextInputLayout exerciseNameLayout = ViewBindings.findChildViewById(rootView, id);
      if (exerciseNameLayout == null) {
        break missingId;
      }

      id = R.id.exerciseTypeDropdown;
      AutoCompleteTextView exerciseTypeDropdown = ViewBindings.findChildViewById(rootView, id);
      if (exerciseTypeDropdown == null) {
        break missingId;
      }

      id = R.id.exerciseTypeLayout;
      TextInputLayout exerciseTypeLayout = ViewBindings.findChildViewById(rootView, id);
      if (exerciseTypeLayout == null) {
        break missingId;
      }

      return new DialogCustomExerciseBinding((ConstraintLayout) rootView, addButton, cancelButton,
          dialogTitle, exerciseNameInput, exerciseNameLayout, exerciseTypeDropdown,
          exerciseTypeLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
