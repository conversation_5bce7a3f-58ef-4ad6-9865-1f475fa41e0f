// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textview.MaterialTextView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemExerciseDefinitionBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton deleteExerciseButton;

  @NonNull
  public final MaterialButton editExerciseButton;

  @NonNull
  public final MaterialTextView exerciseCategoryTextView;

  @NonNull
  public final MaterialTextView exerciseNameTextView;

  private ItemExerciseDefinitionBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton deleteExerciseButton, @NonNull MaterialButton editExerciseButton,
      @NonNull MaterialTextView exerciseCategoryTextView,
      @NonNull MaterialTextView exerciseNameTextView) {
    this.rootView = rootView;
    this.deleteExerciseButton = deleteExerciseButton;
    this.editExerciseButton = editExerciseButton;
    this.exerciseCategoryTextView = exerciseCategoryTextView;
    this.exerciseNameTextView = exerciseNameTextView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemExerciseDefinitionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemExerciseDefinitionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_exercise_definition, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemExerciseDefinitionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.deleteExerciseButton;
      MaterialButton deleteExerciseButton = ViewBindings.findChildViewById(rootView, id);
      if (deleteExerciseButton == null) {
        break missingId;
      }

      id = R.id.editExerciseButton;
      MaterialButton editExerciseButton = ViewBindings.findChildViewById(rootView, id);
      if (editExerciseButton == null) {
        break missingId;
      }

      id = R.id.exerciseCategoryTextView;
      MaterialTextView exerciseCategoryTextView = ViewBindings.findChildViewById(rootView, id);
      if (exerciseCategoryTextView == null) {
        break missingId;
      }

      id = R.id.exerciseNameTextView;
      MaterialTextView exerciseNameTextView = ViewBindings.findChildViewById(rootView, id);
      if (exerciseNameTextView == null) {
        break missingId;
      }

      return new ItemExerciseDefinitionBinding((ConstraintLayout) rootView, deleteExerciseButton,
          editExerciseButton, exerciseCategoryTextView, exerciseNameTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
