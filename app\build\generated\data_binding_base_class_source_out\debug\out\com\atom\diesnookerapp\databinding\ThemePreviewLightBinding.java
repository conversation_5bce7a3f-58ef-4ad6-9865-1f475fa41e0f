// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import com.atom.diesnookerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;

public final class ThemePreviewLightBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final CardView lightThemePreview;

  private ThemePreviewLightBinding(@NonNull CardView rootView,
      @NonNull CardView lightThemePreview) {
    this.rootView = rootView;
    this.lightThemePreview = lightThemePreview;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ThemePreviewLightBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ThemePreviewLightBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.theme_preview_light, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ThemePreviewLightBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    CardView lightThemePreview = (CardView) rootView;

    return new ThemePreviewLightBinding((CardView) rootView, lightThemePreview);
  }
}
