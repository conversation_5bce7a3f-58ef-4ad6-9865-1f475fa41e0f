// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentTaskListBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView categoryTitleTextView;

  @NonNull
  public final TextView pointsTextView;

  @NonNull
  public final RecyclerView tasksRecyclerView;

  private FragmentTaskListBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView categoryTitleTextView, @NonNull TextView pointsTextView,
      @NonNull RecyclerView tasksRecyclerView) {
    this.rootView = rootView;
    this.categoryTitleTextView = categoryTitleTextView;
    this.pointsTextView = pointsTextView;
    this.tasksRecyclerView = tasksRecyclerView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentTaskListBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentTaskListBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_task_list, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentTaskListBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.categoryTitleTextView;
      TextView categoryTitleTextView = ViewBindings.findChildViewById(rootView, id);
      if (categoryTitleTextView == null) {
        break missingId;
      }

      id = R.id.pointsTextView;
      TextView pointsTextView = ViewBindings.findChildViewById(rootView, id);
      if (pointsTextView == null) {
        break missingId;
      }

      id = R.id.tasksRecyclerView;
      RecyclerView tasksRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (tasksRecyclerView == null) {
        break missingId;
      }

      return new FragmentTaskListBinding((ConstraintLayout) rootView, categoryTitleTextView,
          pointsTextView, tasksRecyclerView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
