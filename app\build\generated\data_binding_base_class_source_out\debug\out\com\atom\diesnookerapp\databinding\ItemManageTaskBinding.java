// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemManageTaskBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageButton deleteTaskButton;

  @NonNull
  public final ImageButton editTaskButton;

  @NonNull
  public final TextView taskCategoryTextView;

  @NonNull
  public final TextView taskFrequencyTextView;

  @NonNull
  public final TextView taskPointsTextView;

  @NonNull
  public final TextView taskTitleTextView;

  private ItemManageTaskBinding(@NonNull MaterialCardView rootView,
      @NonNull ImageButton deleteTaskButton, @NonNull ImageButton editTaskButton,
      @NonNull TextView taskCategoryTextView, @NonNull TextView taskFrequencyTextView,
      @NonNull TextView taskPointsTextView, @NonNull TextView taskTitleTextView) {
    this.rootView = rootView;
    this.deleteTaskButton = deleteTaskButton;
    this.editTaskButton = editTaskButton;
    this.taskCategoryTextView = taskCategoryTextView;
    this.taskFrequencyTextView = taskFrequencyTextView;
    this.taskPointsTextView = taskPointsTextView;
    this.taskTitleTextView = taskTitleTextView;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemManageTaskBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemManageTaskBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_manage_task, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemManageTaskBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.deleteTaskButton;
      ImageButton deleteTaskButton = ViewBindings.findChildViewById(rootView, id);
      if (deleteTaskButton == null) {
        break missingId;
      }

      id = R.id.editTaskButton;
      ImageButton editTaskButton = ViewBindings.findChildViewById(rootView, id);
      if (editTaskButton == null) {
        break missingId;
      }

      id = R.id.taskCategoryTextView;
      TextView taskCategoryTextView = ViewBindings.findChildViewById(rootView, id);
      if (taskCategoryTextView == null) {
        break missingId;
      }

      id = R.id.taskFrequencyTextView;
      TextView taskFrequencyTextView = ViewBindings.findChildViewById(rootView, id);
      if (taskFrequencyTextView == null) {
        break missingId;
      }

      id = R.id.taskPointsTextView;
      TextView taskPointsTextView = ViewBindings.findChildViewById(rootView, id);
      if (taskPointsTextView == null) {
        break missingId;
      }

      id = R.id.taskTitleTextView;
      TextView taskTitleTextView = ViewBindings.findChildViewById(rootView, id);
      if (taskTitleTextView == null) {
        break missingId;
      }

      return new ItemManageTaskBinding((MaterialCardView) rootView, deleteTaskButton,
          editTaskButton, taskCategoryTextView, taskFrequencyTextView, taskPointsTextView,
          taskTitleTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
