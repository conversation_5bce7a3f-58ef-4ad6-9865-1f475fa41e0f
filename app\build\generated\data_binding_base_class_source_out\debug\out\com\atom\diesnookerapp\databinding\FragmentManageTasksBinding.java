// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentManageTasksBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final FloatingActionButton addTaskFab;

  @NonNull
  public final TextView emptyViewTasks;

  @NonNull
  public final TextView manageTasksTitleTextView;

  @NonNull
  public final RecyclerView tasksRecyclerView;

  private FragmentManageTasksBinding(@NonNull ConstraintLayout rootView,
      @NonNull FloatingActionButton addTaskFab, @NonNull TextView emptyViewTasks,
      @NonNull TextView manageTasksTitleTextView, @NonNull RecyclerView tasksRecyclerView) {
    this.rootView = rootView;
    this.addTaskFab = addTaskFab;
    this.emptyViewTasks = emptyViewTasks;
    this.manageTasksTitleTextView = manageTasksTitleTextView;
    this.tasksRecyclerView = tasksRecyclerView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentManageTasksBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentManageTasksBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_manage_tasks, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentManageTasksBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addTaskFab;
      FloatingActionButton addTaskFab = ViewBindings.findChildViewById(rootView, id);
      if (addTaskFab == null) {
        break missingId;
      }

      id = R.id.emptyViewTasks;
      TextView emptyViewTasks = ViewBindings.findChildViewById(rootView, id);
      if (emptyViewTasks == null) {
        break missingId;
      }

      id = R.id.manageTasksTitleTextView;
      TextView manageTasksTitleTextView = ViewBindings.findChildViewById(rootView, id);
      if (manageTasksTitleTextView == null) {
        break missingId;
      }

      id = R.id.tasksRecyclerView;
      RecyclerView tasksRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (tasksRecyclerView == null) {
        break missingId;
      }

      return new FragmentManageTasksBinding((ConstraintLayout) rootView, addTaskFab, emptyViewTasks,
          manageTasksTitleTextView, tasksRecyclerView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
