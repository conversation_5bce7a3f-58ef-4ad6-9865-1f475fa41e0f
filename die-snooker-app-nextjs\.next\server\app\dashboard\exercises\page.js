/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/exercises/page";
exports.ids = ["app/dashboard/exercises/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fexercises%2Fpage&page=%2Fdashboard%2Fexercises%2Fpage&appPaths=%2Fdashboard%2Fexercises%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fexercises%2Fpage.tsx&appDir=D%3A%5CHome%5CDokumente%5CAndroidStudioProjects%5CDieSnookerAppGitHub%5Cdie-snooker-app-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CHome%5CDokumente%5CAndroidStudioProjects%5CDieSnookerAppGitHub%5Cdie-snooker-app-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fexercises%2Fpage&page=%2Fdashboard%2Fexercises%2Fpage&appPaths=%2Fdashboard%2Fexercises%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fexercises%2Fpage.tsx&appDir=D%3A%5CHome%5CDokumente%5CAndroidStudioProjects%5CDieSnookerAppGitHub%5Cdie-snooker-app-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CHome%5CDokumente%5CAndroidStudioProjects%5CDieSnookerAppGitHub%5Cdie-snooker-app-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/exercises/page.tsx */ \"(rsc)/./src/app/dashboard/exercises/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'exercises',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module4, \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/exercises/page\",\n        pathname: \"/dashboard/exercises\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZkYXNoYm9hcmQlMkZleGVyY2lzZXMlMkZwYWdlJnBhZ2U9JTJGZGFzaGJvYXJkJTJGZXhlcmNpc2VzJTJGcGFnZSZhcHBQYXRocz0lMkZkYXNoYm9hcmQlMkZleGVyY2lzZXMlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGZGFzaGJvYXJkJTJGZXhlcmNpc2VzJTJGcGFnZS50c3gmYXBwRGlyPUQlM0ElNUNIb21lJTVDRG9rdW1lbnRlJTVDQW5kcm9pZFN0dWRpb1Byb2plY3RzJTVDRGllU25vb2tlckFwcEdpdEh1YiU1Q2RpZS1zbm9va2VyLWFwcC1uZXh0anMlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUQlM0ElNUNIb21lJTVDRG9rdW1lbnRlJTVDQW5kcm9pZFN0dWRpb1Byb2plY3RzJTVDRGllU25vb2tlckFwcEdpdEh1YiU1Q2RpZS1zbm9va2VyLWFwcC1uZXh0anMmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLHNCQUFzQixvSkFBa0o7QUFDeEssc0JBQXNCLDBOQUFnRjtBQUN0RyxzQkFBc0IsME5BQWdGO0FBQ3RHLHNCQUFzQixnT0FBbUY7QUFDekcsc0JBQXNCLHdLQUE2SjtBQUNuTCxvQkFBb0Isd0xBQXNLO0FBR3hMO0FBR0E7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakM7QUFDQTtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0Msc2ZBQTBTO0FBQzlVO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxzZkFBMFM7QUFDOVU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFHckI7QUFDRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFHRTtBQUNGO0FBQ08sd0JBQXdCLHVHQUFrQjtBQUNqRDtBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBtb2R1bGUwID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxIb21lXFxcXERva3VtZW50ZVxcXFxBbmRyb2lkU3R1ZGlvUHJvamVjdHNcXFxcRGllU25vb2tlckFwcEdpdEh1YlxcXFxkaWUtc25vb2tlci1hcHAtbmV4dGpzXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbmNvbnN0IG1vZHVsZTEgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIik7XG5jb25zdCBtb2R1bGUyID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZm9yYmlkZGVuLWVycm9yXCIpO1xuY29uc3QgbW9kdWxlMyA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3VuYXV0aG9yaXplZC1lcnJvclwiKTtcbmNvbnN0IG1vZHVsZTQgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXEhvbWVcXFxcRG9rdW1lbnRlXFxcXEFuZHJvaWRTdHVkaW9Qcm9qZWN0c1xcXFxEaWVTbm9va2VyQXBwR2l0SHViXFxcXGRpZS1zbm9va2VyLWFwcC1uZXh0anNcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcbGF5b3V0LnRzeFwiKTtcbmNvbnN0IHBhZ2U1ID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxIb21lXFxcXERva3VtZW50ZVxcXFxBbmRyb2lkU3R1ZGlvUHJvamVjdHNcXFxcRGllU25vb2tlckFwcEdpdEh1YlxcXFxkaWUtc25vb2tlci1hcHAtbmV4dGpzXFxcXHNyY1xcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXGV4ZXJjaXNlc1xcXFxwYWdlLnRzeFwiKTtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc3NyJ1xufTtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnZGFzaGJvYXJkJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnZXhlcmNpc2VzJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogW3BhZ2U1LCBcIkQ6XFxcXEhvbWVcXFxcRG9rdW1lbnRlXFxcXEFuZHJvaWRTdHVkaW9Qcm9qZWN0c1xcXFxEaWVTbm9va2VyQXBwR2l0SHViXFxcXGRpZS1zbm9va2VyLWFwcC1uZXh0anNcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcZXhlcmNpc2VzXFxcXHBhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICBcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdsYXlvdXQnOiBbbW9kdWxlNCwgXCJEOlxcXFxIb21lXFxcXERva3VtZW50ZVxcXFxBbmRyb2lkU3R1ZGlvUHJvamVjdHNcXFxcRGllU25vb2tlckFwcEdpdEh1YlxcXFxkaWUtc25vb2tlci1hcHAtbmV4dGpzXFxcXHNyY1xcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXGxheW91dC50c3hcIl0sXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgaWNvbjogWyhhc3luYyAocHJvcHMpID0+IChhd2FpdCBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQtbWV0YWRhdGEtaW1hZ2UtbG9hZGVyP3R5cGU9ZmF2aWNvbiZzZWdtZW50PSZiYXNlUGF0aD0mcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyFEOlxcXFxIb21lXFxcXERva3VtZW50ZVxcXFxBbmRyb2lkU3R1ZGlvUHJvamVjdHNcXFxcRGllU25vb2tlckFwcEdpdEh1YlxcXFxkaWUtc25vb2tlci1hcHAtbmV4dGpzXFxcXHNyY1xcXFxhcHBcXFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX19cIikpLmRlZmF1bHQocHJvcHMpKV0sXG4gICAgYXBwbGU6IFtdLFxuICAgIG9wZW5HcmFwaDogW10sXG4gICAgdHdpdHRlcjogW10sXG4gICAgbWFuaWZlc3Q6IHVuZGVmaW5lZFxuICB9XG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdsYXlvdXQnOiBbbW9kdWxlMCwgXCJEOlxcXFxIb21lXFxcXERva3VtZW50ZVxcXFxBbmRyb2lkU3R1ZGlvUHJvamVjdHNcXFxcRGllU25vb2tlckFwcEdpdEh1YlxcXFxkaWUtc25vb2tlci1hcHAtbmV4dGpzXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbbW9kdWxlMSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJdLFxuJ2ZvcmJpZGRlbic6IFttb2R1bGUyLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9mb3JiaWRkZW4tZXJyb3JcIl0sXG4ndW5hdXRob3JpemVkJzogW21vZHVsZTMsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3VuYXV0aG9yaXplZC1lcnJvclwiXSxcbiAgICAgICAgbWV0YWRhdGE6IHtcbiAgICBpY29uOiBbKGFzeW5jIChwcm9wcykgPT4gKGF3YWl0IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC1tZXRhZGF0YS1pbWFnZS1sb2FkZXI/dHlwZT1mYXZpY29uJnNlZ21lbnQ9JmJhc2VQYXRoPSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzIUQ6XFxcXEhvbWVcXFxcRG9rdW1lbnRlXFxcXEFuZHJvaWRTdHVkaW9Qcm9qZWN0c1xcXFxEaWVTbm9va2VyQXBwR2l0SHViXFxcXGRpZS1zbm9va2VyLWFwcC1uZXh0anNcXFxcc3JjXFxcXGFwcFxcXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfX1wiKSkuZGVmYXVsdChwcm9wcykpXSxcbiAgICBhcHBsZTogW10sXG4gICAgb3BlbkdyYXBoOiBbXSxcbiAgICB0d2l0dGVyOiBbXSxcbiAgICBtYW5pZmVzdDogdW5kZWZpbmVkXG4gIH1cbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0uY2hpbGRyZW47XG5jb25zdCBwYWdlcyA9IFtcIkQ6XFxcXEhvbWVcXFxcRG9rdW1lbnRlXFxcXEFuZHJvaWRTdHVkaW9Qcm9qZWN0c1xcXFxEaWVTbm9va2VyQXBwR2l0SHViXFxcXGRpZS1zbm9va2VyLWFwcC1uZXh0anNcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcZXhlcmNpc2VzXFxcXHBhZ2UudHN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuY29uc3QgX19uZXh0X2FwcF9yZXF1aXJlX18gPSBfX3dlYnBhY2tfcmVxdWlyZV9fXG5jb25zdCBfX25leHRfYXBwX2xvYWRfY2h1bmtfXyA9ICgpID0+IFByb21pc2UucmVzb2x2ZSgpXG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBQYWdlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICBwYWdlOiBcIi9kYXNoYm9hcmQvZXhlcmNpc2VzL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2Rhc2hib2FyZC9leGVyY2lzZXNcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fexercises%2Fpage&page=%2Fdashboard%2Fexercises%2Fpage&appPaths=%2Fdashboard%2Fexercises%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fexercises%2Fpage.tsx&appDir=D%3A%5CHome%5CDokumente%5CAndroidStudioProjects%5CDieSnookerAppGitHub%5Cdie-snooker-app-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CHome%5CDokumente%5CAndroidStudioProjects%5CDieSnookerAppGitHub%5Cdie-snooker-app-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/AuthContext.tsx */ \"(rsc)/./src/context/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cexercises%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cexercises%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/exercises/page.tsx */ \"(rsc)/./src/app/dashboard/exercises/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNIb21lJTVDJTVDRG9rdW1lbnRlJTVDJTVDQW5kcm9pZFN0dWRpb1Byb2plY3RzJTVDJTVDRGllU25vb2tlckFwcEdpdEh1YiU1QyU1Q2RpZS1zbm9va2VyLWFwcC1uZXh0anMlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNleGVyY2lzZXMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0xBQXNLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxIb21lXFxcXERva3VtZW50ZVxcXFxBbmRyb2lkU3R1ZGlvUHJvamVjdHNcXFxcRGllU25vb2tlckFwcEdpdEh1YlxcXFxkaWUtc25vb2tlci1hcHAtbmV4dGpzXFxcXHNyY1xcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXGV4ZXJjaXNlc1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cexercises%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNIb21lJTVDJTVDRG9rdW1lbnRlJTVDJTVDQW5kcm9pZFN0dWRpb1Byb2plY3RzJTVDJTVDRGllU25vb2tlckFwcEdpdEh1YiU1QyU1Q2RpZS1zbm9va2VyLWFwcC1uZXh0anMlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBNkoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXEhvbWVcXFxcRG9rdW1lbnRlXFxcXEFuZHJvaWRTdHVkaW9Qcm9qZWN0c1xcXFxEaWVTbm9va2VyQXBwR2l0SHViXFxcXGRpZS1zbm9va2VyLWFwcC1uZXh0anNcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxIb21lXFxEb2t1bWVudGVcXEFuZHJvaWRTdHVkaW9Qcm9qZWN0c1xcRGllU25vb2tlckFwcEdpdEh1YlxcZGllLXNub29rZXItYXBwLW5leHRqc1xcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/exercises/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/dashboard/exercises/page.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerAppGitHub\\die-snooker-app-nextjs\\src\\app\\dashboard\\exercises\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerAppGitHub\\die-snooker-app-nextjs\\src\\app\\dashboard\\layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcSG9tZVxcRG9rdW1lbnRlXFxBbmRyb2lkU3R1ZGlvUHJvamVjdHNcXERpZVNub29rZXJBcHBHaXRIdWJcXGRpZS1zbm9va2VyLWFwcC1uZXh0anNcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcxOWNiMGZjM2Y2M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/AuthContext */ \"(rsc)/./src/context/AuthContext.tsx\");\n\n\n\n\n // Import AuthProvider\nconst metadata = {\n    title: \"Die Snooker App\",\n    description: \"Track your snooker progress and connect with trainers.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        children: [\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerAppGitHub\\die-snooker-app-nextjs\\src\\context\\AuthContext.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerAppGitHub\\die-snooker-app-nextjs\\src\\context\\AuthContext.tsx",
"useAuth",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/AuthContext.tsx */ \"(ssr)/./src/context/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cexercises%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cexercises%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/exercises/page.tsx */ \"(ssr)/./src/app/dashboard/exercises/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNIb21lJTVDJTVDRG9rdW1lbnRlJTVDJTVDQW5kcm9pZFN0dWRpb1Byb2plY3RzJTVDJTVDRGllU25vb2tlckFwcEdpdEh1YiU1QyU1Q2RpZS1zbm9va2VyLWFwcC1uZXh0anMlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNleGVyY2lzZXMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0xBQXNLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxIb21lXFxcXERva3VtZW50ZVxcXFxBbmRyb2lkU3R1ZGlvUHJvamVjdHNcXFxcRGllU25vb2tlckFwcEdpdEh1YlxcXFxkaWUtc25vb2tlci1hcHAtbmV4dGpzXFxcXHNyY1xcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXGV4ZXJjaXNlc1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cexercises%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(ssr)/./src/app/dashboard/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNIb21lJTVDJTVDRG9rdW1lbnRlJTVDJTVDQW5kcm9pZFN0dWRpb1Byb2plY3RzJTVDJTVDRGllU25vb2tlckFwcEdpdEh1YiU1QyU1Q2RpZS1zbm9va2VyLWFwcC1uZXh0anMlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBNkoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXEhvbWVcXFxcRG9rdW1lbnRlXFxcXEFuZHJvaWRTdHVkaW9Qcm9qZWN0c1xcXFxEaWVTbm9va2VyQXBwR2l0SHViXFxcXGRpZS1zbm9va2VyLWFwcC1uZXh0anNcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/exercises/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/dashboard/exercises/page.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_auth_withAuth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/auth/withAuth */ \"(ssr)/./src/components/auth/withAuth.tsx\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react_chartjs_2__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-chartjs-2 */ \"(ssr)/./node_modules/react-chartjs-2/dist/index.js\");\n/* harmony import */ var chart_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! chart.js */ \"(ssr)/./node_modules/chart.js/dist/chart.js\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(ssr)/./src/components/ui/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nchart_js__WEBPACK_IMPORTED_MODULE_7__.Chart.register(chart_js__WEBPACK_IMPORTED_MODULE_7__.CategoryScale, chart_js__WEBPACK_IMPORTED_MODULE_7__.LinearScale, chart_js__WEBPACK_IMPORTED_MODULE_7__.PointElement, chart_js__WEBPACK_IMPORTED_MODULE_7__.LineElement, chart_js__WEBPACK_IMPORTED_MODULE_7__.Title, chart_js__WEBPACK_IMPORTED_MODULE_7__.Tooltip, chart_js__WEBPACK_IMPORTED_MODULE_7__.Legend);\nfunction ExercisesPage() {\n    const { currentUser } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [exerciseDefinitions, setExerciseDefinitions] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [exerciseRecords, setExerciseRecords] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [groupedExercisesWithRecords, setGroupedExercisesWithRecords] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ExercisesPage.useEffect\": ()=>{\n            if (!currentUser) return;\n            const fetchData = {\n                \"ExercisesPage.useEffect.fetchData\": async ()=>{\n                    setLoading(true);\n                    setError(null);\n                    try {\n                        // 1. Fetch User Exercise Definitions\n                        const defsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"user_exercise_definitions\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.where)(\"userId\", \"==\", currentUser.uid));\n                        const defsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDocs)(defsQuery);\n                        const fetchedDefinitions = defsSnapshot.docs.map({\n                            \"ExercisesPage.useEffect.fetchData.fetchedDefinitions\": (doc)=>({\n                                    id: doc.id,\n                                    ...doc.data()\n                                })\n                        }[\"ExercisesPage.useEffect.fetchData.fetchedDefinitions\"]);\n                        setExerciseDefinitions(fetchedDefinitions);\n                        // 2. Fetch Exercise Records\n                        const recsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"exercise_records\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.where)(\"userId\", \"==\", currentUser.uid), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.orderBy)(\"timestamp\", \"desc\"));\n                        const recsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDocs)(recsQuery);\n                        const fetchedRecords = recsSnapshot.docs.map({\n                            \"ExercisesPage.useEffect.fetchData.fetchedRecords\": (doc)=>({\n                                    id: doc.id,\n                                    ...doc.data()\n                                })\n                        }[\"ExercisesPage.useEffect.fetchData.fetchedRecords\"]);\n                        setExerciseRecords(fetchedRecords);\n                        // 3. Combine and Group Data\n                        const exercisesWithRecs = fetchedDefinitions.map({\n                            \"ExercisesPage.useEffect.fetchData.exercisesWithRecs\": (def)=>{\n                                const relevantRecords = fetchedRecords.filter({\n                                    \"ExercisesPage.useEffect.fetchData.exercisesWithRecs.relevantRecords\": (rec)=>rec.exerciseId === def.id\n                                }[\"ExercisesPage.useEffect.fetchData.exercisesWithRecs.relevantRecords\"]).sort({\n                                    \"ExercisesPage.useEffect.fetchData.exercisesWithRecs.relevantRecords\": (a, b)=>{\n                                        const timeA = a.timestamp && typeof a.timestamp.toMillis === 'function' ? a.timestamp.toMillis() : typeof a.timestamp === 'number' ? a.timestamp : 0;\n                                        const timeB = b.timestamp && typeof b.timestamp.toMillis === 'function' ? b.timestamp.toMillis() : typeof b.timestamp === 'number' ? b.timestamp : 0;\n                                        return timeB - timeA; // Sort descending (most recent first for display list)\n                                    }\n                                }[\"ExercisesPage.useEffect.fetchData.exercisesWithRecs.relevantRecords\"]);\n                                return {\n                                    ...def,\n                                    records: relevantRecords\n                                };\n                            }\n                        }[\"ExercisesPage.useEffect.fetchData.exercisesWithRecs\"]);\n                        const grouped = {};\n                        exercisesWithRecs.forEach({\n                            \"ExercisesPage.useEffect.fetchData\": (ex)=>{\n                                if (!grouped[ex.category]) {\n                                    grouped[ex.category] = [];\n                                }\n                                grouped[ex.category].push(ex);\n                            }\n                        }[\"ExercisesPage.useEffect.fetchData\"]);\n                        setGroupedExercisesWithRecords(grouped);\n                    } catch (err) {\n                        console.error(\"Error fetching exercises data:\", err);\n                        setError(\"Failed to load exercises data. \" + err.message);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"ExercisesPage.useEffect.fetchData\"];\n            fetchData();\n        }\n    }[\"ExercisesPage.useEffect\"], [\n        currentUser\n    ]);\n    const formatDateTimestamp = (timestamp, options)=>{\n        const defaultOptions = {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        };\n        const fmtOptions = options || defaultOptions;\n        if (timestamp && typeof timestamp.toDate === 'function') {\n            return new Date(timestamp.toDate()).toLocaleDateString('de-DE', fmtOptions);\n        }\n        if (typeof timestamp === 'number') {\n            return new Date(timestamp).toLocaleDateString('de-DE', fmtOptions);\n        }\n        if (timestamp instanceof Date) {\n            return timestamp.toLocaleDateString('de-DE', fmtOptions);\n        }\n        return 'Invalid Date'; // Fallback\n    };\n    const getChartData = (records)=>{\n        const filteredRecords = records.filter((r)=>typeof r.score === 'number').sort((a, b)=>{\n            const timeA = a.timestamp && typeof a.timestamp.toMillis === 'function' ? a.timestamp.toMillis() : typeof a.timestamp === 'number' ? a.timestamp : 0;\n            const timeB = b.timestamp && typeof b.timestamp.toMillis === 'function' ? b.timestamp.toMillis() : typeof b.timestamp === 'number' ? b.timestamp : 0;\n            return timeA - timeB;\n        });\n        return {\n            labels: filteredRecords.map((r)=>formatDateTimestamp(r.timestamp, {\n                    year: 'numeric',\n                    month: '2-digit',\n                    day: '2-digit'\n                })),\n            datasets: [\n                {\n                    label: 'Score',\n                    data: filteredRecords.map((r)=>r.score),\n                    fill: false,\n                    borderColor: 'rgb(238, 187, 195)',\n                    tension: 0.1\n                }\n            ]\n        };\n    };\n    const chartOptions = (exerciseName)=>({\n            responsive: true,\n            maintainAspectRatio: false,\n            plugins: {\n                legend: {\n                    display: false\n                },\n                title: {\n                    display: true,\n                    text: `Score History: ${exerciseName}`\n                }\n            },\n            scales: {\n                y: {\n                    beginAtZero: true\n                }\n            }\n        });\n    if (loading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n        lineNumber: 166,\n        columnNumber: 23\n    }, this);\n    if (error) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-red-500 p-4 bg-red-100 rounded-md\",\n        children: error\n    }, void 0, false, {\n        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n        lineNumber: 167,\n        columnNumber: 21\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-3xl font-bold mb-6 text-gray-800\",\n                children: \"\\xdcbungen\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this),\n            Object.keys(groupedExercisesWithRecords).length === 0 && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600\",\n                children: \"No exercises defined yet. Please add some exercises.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                lineNumber: 173,\n                columnNumber: 9\n            }, this),\n            Object.entries(groupedExercisesWithRecords).map(([category, exercises])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4 text-indigo-700 border-b-2 border-indigo-200 pb-2\",\n                            children: category\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: exercises.map((exercise)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                    className: \"bg-white p-4 rounded-lg shadow-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                            className: \"font-medium text-lg cursor-pointer text-gray-800 hover:text-indigo-600\",\n                                            children: [\n                                                exercise.name,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500 ml-2\",\n                                                    children: [\n                                                        \"(\",\n                                                        exercise.exerciseType,\n                                                        \") - \",\n                                                        exercise.records.length,\n                                                        \" record(s)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4\",\n                                            children: [\n                                                exercise.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mb-3\",\n                                                    children: exercise.description\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 44\n                                                }, this),\n                                                exercise.exerciseType === 'splits' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SplitsExerciseView, {\n                                                    records: exercise.records,\n                                                    formatDate: formatDateTimestamp\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        exercise.records.filter((r)=>typeof r.score === 'number').length > 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-64 mb-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_chartjs_2__WEBPACK_IMPORTED_MODULE_8__.Line, {\n                                                                options: chartOptions(exercise.name),\n                                                                data: getChartData(exercise.records)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 25\n                                                        }, this) : exercise.records.filter((r)=>typeof r.score === 'number').length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 mb-3\",\n                                                            children: \"Not enough score data for a trend chart.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 25\n                                                        }, this) : null,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-md text-gray-700 mb-2\",\n                                                            children: \"Recent Records:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        exercise.records.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"list-disc list-inside pl-4 space-y-1 text-sm\",\n                                                            children: exercise.records.slice(0, 5).map((record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: [\n                                                                        formatDateTimestamp(record.timestamp),\n                                                                        \":\",\n                                                                        typeof record.score === 'number' && ` Score: ${record.score}`,\n                                                                        typeof record.timeInMinutes === 'number' && ` Time: ${record.timeInMinutes} min`\n                                                                    ]\n                                                                }, record.id, true, {\n                                                                    fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"No records for this exercise yet.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, exercise.id, true, {\n                                    fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, category, true, {\n                    fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this))\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n        lineNumber: 170,\n        columnNumber: 5\n    }, this);\n}\n// New component for displaying 'splits' exercises\nconst SplitsExerciseView = ({ records, formatDate })=>{\n    if (records.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: \"No records for this exercise yet.\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n            lineNumber: 230,\n            columnNumber: 12\n        }, undefined);\n    }\n    // Group records by date string\n    const groupedByDate = records.reduce((acc, record)=>{\n        const dateKey = formatDate(record.timestamp, {\n            year: 'numeric',\n            month: '2-digit',\n            day: '2-digit'\n        });\n        if (!acc[dateKey]) {\n            acc[dateKey] = [];\n        }\n        acc[dateKey].push(record);\n        return acc;\n    }, {});\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n            className: \"min-w-full text-sm text-left text-gray-500\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                    className: \"text-xs text-gray-700 uppercase bg-gray-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                scope: \"col\",\n                                className: \"px-4 py-3\",\n                                children: \"Date\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                scope: \"col\",\n                                className: \"px-4 py-3 text-center\",\n                                children: \"3er\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                scope: \"col\",\n                                className: \"px-4 py-3 text-center\",\n                                children: \"6er\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                scope: \"col\",\n                                className: \"px-4 py-3 text-center\",\n                                children: \"10er\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                    children: Object.entries(groupedByDate).map(([date, dateRecords])=>{\n                        const score3er = dateRecords.find((r)=>r.spelt === '3')?.score;\n                        const score6er = dateRecords.find((r)=>r.spelt === '6')?.score;\n                        const score10er = dateRecords.find((r)=>r.spelt === '10')?.score;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: \"bg-white border-b hover:bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-3 font-medium text-gray-900 whitespace-nowrap\",\n                                    children: date\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-3 text-center\",\n                                    children: typeof score3er === 'number' ? score3er : '-'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-3 text-center\",\n                                    children: typeof score6er === 'number' ? score6er : '-'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-3 text-center\",\n                                    children: typeof score10er === 'number' ? score10er : '-'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, date, true, {\n                            fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n            lineNumber: 245,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\exercises\\\\page.tsx\",\n        lineNumber: 244,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,_components_auth_withAuth__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(ExercisesPage, {\n    redirectTo: '/login'\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/exercises/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PlayerDashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n // To display user info or role\nconst NavLink = ({ href, children })=>{\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const isActive = pathname === href;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n        href: href,\n        className: `block px-4 py-2 rounded-md text-sm font-medium transition-colors\n      ${isActive ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-600 hover:text-white'}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, undefined);\n};\nfunction PlayerDashboardLayout({ children }) {\n    const { currentUser, userProfile } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const handleLogout = async ()=>{\n        try {\n            await _lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth.signOut();\n            router.push('/login');\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        }\n    };\n    // If auth is still loading, or no user, show loading or redirect (handled by withAuth on page level)\n    // This layout assumes it's rendered for an authenticated user.\n    // Adding a basic loading check here for robustness.\n    if (!currentUser && !userProfile) {\n        // This state should ideally be handled by page-level withAuth HOC redirecting.\n        // If somehow layout is shown without user, it might be during brief transition.\n        // Can show a minimal loading or empty state.\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"Loading user...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 45,\n                columnNumber: 77\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 45,\n            columnNumber: 14\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col md:flex-row bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"w-full md:w-64 bg-indigo-800 text-white flex-shrink-0 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-5 border-b border-indigo-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-semibold\",\n                                children: \"Die Snooker App\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this),\n                            userProfile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 text-sm text-indigo-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: userProfile.displayName || currentUser?.email\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"Role: \",\n                                            userProfile.role\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"p-3 space-y-1 flex-grow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                href: \"/dashboard\",\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this),\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                href: \"/dashboard/self-assessment\",\n                                children: \"Selbsteinsch\\xe4tzung\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                href: \"/dashboard/exercises\",\n                                children: \"\\xdcbungen\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                href: \"/dashboard/tasks\",\n                                children: \"Aufgaben\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                href: \"/dashboard/training-plan\",\n                                children: \"Trainingsplan\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                href: \"/dashboard/trainer-connections\",\n                                children: \"Trainer-Verbindungen\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-5 border-t border-indigo-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleLogout,\n                            className: \"w-full bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded text-sm transition-colors\",\n                            children: \"Logout\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 p-6 overflow-auto\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/withAuth.tsx":
/*!******************************************!*\
  !*** ./src/components/auth/withAuth.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(ssr)/./src/components/ui/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n // We'll create this next\nfunction withAuth(WrappedComponent, options = {}) {\n    const WithAuthComponent = (props)=>{\n        const { currentUser, userProfile, loading } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n        const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n        const { roles, redirectTo } = options;\n        (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n            \"withAuth.WithAuthComponent.useEffect\": ()=>{\n                if (loading) {\n                    return; // Wait for auth state to load\n                }\n                if (!currentUser) {\n                    router.replace(redirectTo || '/login'); // Default to player login\n                    return;\n                }\n                // Role check if roles are specified\n                if (roles && roles.length > 0) {\n                    if (!userProfile || !roles.includes(userProfile.role)) {\n                        // If roles are specified and user doesn't have one, redirect.\n                        // This could be to a generic access denied page or back to their default dashboard/login.\n                        // For simplicity, redirecting to player login if trainer role check fails.\n                        router.replace(redirectTo || (userProfile?.role === 'PLAYER' ? '/dashboard' : '/login'));\n                    }\n                }\n            }\n        }[\"withAuth.WithAuthComponent.useEffect\"], [\n            currentUser,\n            userProfile,\n            loading,\n            router,\n            roles,\n            redirectTo\n        ]);\n        if (loading || !currentUser) {\n            // Show a loading spinner or a blank page while checking auth\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\components\\\\auth\\\\withAuth.tsx\",\n                lineNumber: 46,\n                columnNumber: 14\n            }, this);\n        }\n        // Additional role check before rendering the component, in case useEffect hasn't run yet or for strictness\n        if (roles && roles.length > 0 && (!userProfile || !roles.includes(userProfile.role))) {\n            // This check ensures that if the user is logged in but doesn't have the required role,\n            // they don't briefly see the component content.\n            // It might be redundant if useEffect handles redirection quickly, but adds robustness.\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\components\\\\auth\\\\withAuth.tsx\",\n                lineNumber: 54,\n                columnNumber: 16\n            }, this); // Or an \"Access Denied\" component\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WrappedComponent, {\n            ...props\n        }, void 0, false, {\n            fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\components\\\\auth\\\\withAuth.tsx\",\n            lineNumber: 58,\n            columnNumber: 12\n        }, this);\n    };\n    // Set a display name for the HOC for better debugging\n    WithAuthComponent.displayName = `WithAuth(${WrappedComponent.displayName || WrappedComponent.name || 'Component'})`;\n    return WithAuthComponent;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/withAuth.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/LoadingSpinner.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/LoadingSpinner.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoadingSpinner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction LoadingSpinner() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center min-h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-indigo-600\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9Mb2FkaW5nU3Bpbm5lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUVlLFNBQVNBO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs7Ozs7Ozs7OztBQUdyQiIsInNvdXJjZXMiOlsiRDpcXEhvbWVcXERva3VtZW50ZVxcQW5kcm9pZFN0dWRpb1Byb2plY3RzXFxEaWVTbm9va2VyQXBwR2l0SHViXFxkaWUtc25vb2tlci1hcHAtbmV4dGpzXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxMb2FkaW5nU3Bpbm5lci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmdTcGlubmVyKCkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWluLWgtc2NyZWVuXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0xNiB3LTE2IGJvcmRlci10LTQgYm9yZGVyLWItNCBib3JkZXItaW5kaWdvLTYwMFwiPjwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkxvYWRpbmdTcGlubmVyIiwiZGl2IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/LoadingSpinner.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [currentUser, setCurrentUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, {\n                \"AuthProvider.useEffect.unsubscribe\": async (user)=>{\n                    setLoading(true);\n                    setError(null);\n                    setCurrentUser(user);\n                    setUserProfile(null); // Reset profile on auth state change\n                    if (user) {\n                        try {\n                            const userProfileRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"user_profiles\", user.uid);\n                            const userProfileSnap = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)(userProfileRef);\n                            if (userProfileSnap.exists()) {\n                                setUserProfile(userProfileSnap.data());\n                            } else {\n                                // This case should ideally be handled at login/registration\n                                // For example, creating a default profile if one doesn't exist.\n                                console.warn(\"User profile does not exist for UID:\", user.uid);\n                            }\n                        } catch (e) {\n                            console.error(\"Error fetching user profile:\", e);\n                            setError(e);\n                        }\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.unsubscribe\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>unsubscribe()\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            currentUser,\n            userProfile,\n            loading,\n            error\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\context\\\\AuthContext.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/context/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   app: () => (/* binding */ app),\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(ssr)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyB-fFmAiherpGg3dbecT3v_Z-368kSEMPY\",\n    authDomain: \"die-snooker-app.firebaseapp.com\",\n    projectId: \"die-snooker-app\",\n    storageBucket: \"die-snooker-app.firebasestorage.app\",\n    messagingSenderId: \"547283642216\",\n    appId: \"1:547283642216:web:7f2fdc23dab5ce8430d8dd\",\n    measurementId: \"G-GTFVZZ4LJ\"\n};\nlet app;\nlet auth;\nlet db;\nif ((0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length === 0) {\n    app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n} else {\n    app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)()[0];\n}\nauth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\ndb = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@firebase","vendor-chunks/@grpc","vendor-chunks/protobufjs","vendor-chunks/long","vendor-chunks/@protobufjs","vendor-chunks/lodash.camelcase","vendor-chunks/tslib","vendor-chunks/idb","vendor-chunks/@swc","vendor-chunks/firebase","vendor-chunks/chart.js","vendor-chunks/@kurkle","vendor-chunks/react-chartjs-2"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fexercises%2Fpage&page=%2Fdashboard%2Fexercises%2Fpage&appPaths=%2Fdashboard%2Fexercises%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fexercises%2Fpage.tsx&appDir=D%3A%5CHome%5CDokumente%5CAndroidStudioProjects%5CDieSnookerAppGitHub%5Cdie-snooker-app-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CHome%5CDokumente%5CAndroidStudioProjects%5CDieSnookerAppGitHub%5Cdie-snooker-app-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();