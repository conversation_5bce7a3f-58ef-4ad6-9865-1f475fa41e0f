[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_dropdown_background_dark_blue_dark.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\dropdown_background_dark_blue_dark.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_manage_tasks.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_manage_tasks.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_item_task_completion_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_task_completion_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_item_history_detail.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_history_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\anim_slide_down.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\anim\\slide_down.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_item_exercise_history.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_exercise_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_questions_before_training.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_questions_before_training.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_item_legend.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_legend.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_before_training.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_before_training.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_dropdown_background_snooker.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\dropdown_background_snooker.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_breakbuilding.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_breakbuilding.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_theme_preview_light.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\theme_preview_light.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_dropdown_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\dropdown_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_dropdown_background_solid_snooker.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\dropdown_background_solid_snooker.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_exercise_detail_splits.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_exercise_detail_splits.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_theme_preview_neon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\theme_preview_neon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\mipmap-mdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_trainingsplan_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\trainingsplan_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_exercise_selection_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\exercise_selection_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\mipmap-xxxhdpi_ic_launcher_background.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\mipmap-xxxhdpi\\ic_launcher_background.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_task_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_task_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_exercise_detail_stellungsspiel.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_exercise_detail_stellungsspiel.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_item_manage_exercise_category_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_manage_exercise_category_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_dialog_select_points.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\dialog_select_points.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_dropdown_background_solid_dark_blue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\dropdown_background_solid_dark_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_dropdown_background_ocean.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\dropdown_background_ocean.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_expand_more.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_expand_more.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_dropdown_background_crimson.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\dropdown_background_crimson.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_dropdown_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\dropdown_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_dropdown_background_dark_blue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\dropdown_background_dark_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_dropdown_background_blue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\dropdown_background_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_history.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_profile.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_dropdown_background_snooker_dark.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\dropdown_background_snooker_dark.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_assessment_new.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_assessment_new.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_graph.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_graph.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\menu_menu_history.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\menu\\menu_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_notification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_notification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_marker_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\marker_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_theme_preview_crimson.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\theme_preview_crimson.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\menu_bottom_nav_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\menu\\bottom_nav_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_theme_preview_dark_blue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\theme_preview_dark_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_item_manage_task_category_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_manage_task_category_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_assessment.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_assessment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_dropdown_background_blue_dark.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\dropdown_background_blue_dark.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_tasks.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_tasks.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_dropdown_background_solid_dark.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\dropdown_background_solid_dark.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_theme_preview_ocean.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\theme_preview_ocean.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\mipmap-xxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_questions_after_tournament.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_questions_after_tournament.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_results.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_results.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_dialog_manual_time_entry.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\dialog_manual_time_entry.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_item_date.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_date.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_item_training_assessment.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_training_assessment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_technik.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_technik.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_register.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_register.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_item_question.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_question.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_dropdown_background_solid_neon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\dropdown_background_solid_neon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_expand_less.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_expand_less.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_dropdown_background_crimson_dark.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\dropdown_background_crimson_dark.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_selbsteinschaetzung.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_selbsteinschaetzung.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_results_new.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_results_new.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\mipmap-xhdpi_ic_launcher_background.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\mipmap-xhdpi\\ic_launcher_background.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_delete.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_delete.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_dropdown_background_neon_dark.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\dropdown_background_neon_dark.xml"}, {"merged": "com.atom.diesnookerapp-debug-83:/menu_menu_manage_exercises.xml.flat", "source": "com.atom.diesnookerapp-main-85:/menu/menu_manage_exercises.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_theme_preview_dark.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\theme_preview_dark.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_marker_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\marker_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_item_exercise_definition.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_exercise_definition.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_questions.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_questions.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\mipmap-xxxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_questions_after_training.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_questions_after_training.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\mipmap-mdpi_ic_launcher_background.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\mipmap-mdpi\\ic_launcher_background.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_trainingsplan_history_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\trainingsplan_history_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_tasks_new.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_tasks_new.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_dialog_add_edit_exercise.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\dialog_add_edit_exercise.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_item_user_connection.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_user_connection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\menu_menu_timeframe_simple.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\menu\\menu_timeframe_simple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_dropdown_background_solid_default.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\dropdown_background_solid_default.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_before_tournament.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_before_tournament.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_item_task_history.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_task_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_after_tournament.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_after_tournament.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\mipmap-hdpi_ic_launcher_background.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\mipmap-hdpi\\ic_launcher_background.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_dropdown_background_solid_blue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\dropdown_background_solid_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_item_history.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_dialog_legend.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\dialog_legend.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\mipmap-hdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_item_exercise.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_exercise.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_add.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_add.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\navigation_nav_graph.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\navigation\\nav_graph.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_item_month_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_month_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_training_new.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_training_new.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_trainingsplan.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_trainingsplan.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_trainingsplan_history.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_trainingsplan_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_ergebniserfassung.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_ergebniserfassung.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_generic_exercise_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_generic_exercise_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_exercise_history.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_exercise_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_remove_exercise_trainingsplan.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_remove_exercise_trainingsplan.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_exercise_history_detail.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_exercise_history_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_history_detail.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_history_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_exercise_detail.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_exercise_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_timer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_timer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_potting.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_potting.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_aufgaben.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_aufgaben.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_dropdown_background_dark.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\dropdown_background_dark.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_manage_exercises.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_manage_exercises.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_item_task_completion.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_task_completion.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\mipmap-xhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_save.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_save.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_dropdown_background_solid_ocean.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\dropdown_background_solid_ocean.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\menu_menu_timeframe.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\menu\\menu_timeframe.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_graph_detail.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_graph_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_item_assessment.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_assessment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_safeties.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_safeties.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_splits.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_splits.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_dialog_custom_exercise.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\dialog_custom_exercise.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_graph.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_graph.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_calendar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_calendar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_dialog_custom_timeframe.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\dialog_custom_timeframe.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_arrow_forward.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_arrow_forward.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_dialog_exercise_selection.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\dialog_exercise_selection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_task_history.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_task_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_dropdown_background_solid_crimson.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\dropdown_background_solid_crimson.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\navigation_mobile_navigation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\navigation\\mobile_navigation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_exercise_detail_timeonly.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_exercise_detail_timeonly.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_item_manage_task.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_manage_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_item_breakbuilding.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_breakbuilding.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_dialog_add_exercise.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\dialog_add_exercise.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_item_task_category_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_task_category_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_item_exercise_category.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_exercise_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\anim_slide_up.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\anim\\slide_up.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_after_training.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_after_training.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_questions_before_tournament.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_questions_before_tournament.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_dialog_add_edit_task.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\dialog_add_edit_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_dropdown_background_neon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\dropdown_background_neon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\navigation_auth_nav_graph.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\navigation\\auth_nav_graph.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_baseline_attractions_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\baseline_attractions_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_dropdown_background_ocean_dark.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\dropdown_background_ocean_dark.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_exercise_selection.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_exercise_selection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_exercise_graph.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_exercise_graph.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_theme_preview_snooker.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\theme_preview_snooker.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_item_task_category.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_task_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_theme_preview_blue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\theme_preview_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_item_task.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_stellungsspiel.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_stellungsspiel.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_history.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_add_exercise_trainingsplan.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_add_exercise_trainingsplan.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\drawable_ic_fitness.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\drawable\\ic_fitness.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\mipmap-xxhdpi_ic_launcher_background.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\mipmap-xxhdpi\\ic_launcher_background.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_dialog_edit_connection.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\dialog_edit_connection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_fragment_user_management.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_user_management.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-debug-83:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\activity_main.xml"}]