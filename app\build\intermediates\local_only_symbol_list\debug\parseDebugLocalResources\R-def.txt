R_DEF: Internal format may change without notice
local
anim slide_down
anim slide_up
array debug_menu_options
color black
color blue_200
color blue_500
color blue_700
color blue_card_background_dark
color blue_card_background_light
color crimson_500
color crimson_card_background_dark
color crimson_card_background_light
color crimson_dark
color crimson_darkest
color crimson_light
color crimson_lightest
color crimson_medium
color cyan_200
color cyan_500
color cyan_700
color dark_blue_200
color dark_blue_500
color dark_blue_700
color dark_blue_card_background_dark
color dark_blue_card_background_light
color dark_card_background
color dark_gray
color dark_hint_color
color dark_icon_color
color dark_stroke_color
color ic_launcher_background
color light_card_background
color light_gray
color light_hint_color
color light_icon_color
color light_stroke_color
color neon_500
color neon_card_background_dark
color neon_card_background_light
color neon_pink_1
color neon_pink_2
color neon_pink_3
color neon_purple_1
color neon_purple_2
color neon_purple_3
color neon_purple_4
color neon_purple_5
color neon_purple_6
color neon_purple_7
color ocean_500
color ocean_card_background_dark
color ocean_card_background_light
color ocean_dark
color ocean_darkest
color ocean_light
color ocean_lightest
color ocean_medium
color orange_200
color orange_500
color orange_700
color purple_200
color purple_500
color purple_700
color selected_color
color snooker_black
color snooker_blue
color snooker_brown
color snooker_card_background_dark
color snooker_card_background_light
color snooker_green_200
color snooker_green_500
color snooker_green_700
color snooker_pink
color snooker_red_200
color snooker_red_500
color snooker_red_700
color snooker_yellow
color teal_200
color teal_700
color white
dimen dialog_input_min_height
dimen legend_dialog_width
drawable baseline_attractions_24
drawable dropdown_background
drawable dropdown_background_blue
drawable dropdown_background_blue_dark
drawable dropdown_background_crimson
drawable dropdown_background_crimson_dark
drawable dropdown_background_dark
drawable dropdown_background_dark_blue
drawable dropdown_background_dark_blue_dark
drawable dropdown_background_neon
drawable dropdown_background_neon_dark
drawable dropdown_background_ocean
drawable dropdown_background_ocean_dark
drawable dropdown_background_snooker
drawable dropdown_background_snooker_dark
drawable dropdown_background_solid_blue
drawable dropdown_background_solid_crimson
drawable dropdown_background_solid_dark
drawable dropdown_background_solid_dark_blue
drawable dropdown_background_solid_default
drawable dropdown_background_solid_neon
drawable dropdown_background_solid_ocean
drawable dropdown_background_solid_snooker
drawable ic_add
drawable ic_add_exercise_trainingsplan
drawable ic_arrow_forward
drawable ic_assessment
drawable ic_assessment_new
drawable ic_calendar
drawable ic_delete
drawable ic_expand_less
drawable ic_expand_more
drawable ic_fitness
drawable ic_graph
drawable ic_history
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_notification
drawable ic_remove_exercise_trainingsplan
drawable ic_results
drawable ic_results_new
drawable ic_save
drawable ic_settings
drawable ic_tasks
drawable ic_tasks_new
drawable ic_timer
drawable ic_training_new
drawable marker_background
id accountCard
id actionToAfterTournament
id actionToAfterTraining
id actionToBeforeTournament
id actionToBeforeTraining
id actionToQuestions
id action_all_time
id action_aufgaben_to_category_tasks
id action_breakbuildingFragment_to_exerciseDetailFragment
id action_clear
id action_custom
id action_ergebniserfassung_to_generic_exercise_list
id action_exerciseDetailFragment_to_exerciseGraphFragment
id action_exerciseHistory_to_exerciseGraph
id action_exercise_history_to_detail
id action_fix_migration
id action_genericExerciseListFragment_to_exerciseDetailFragment
id action_graphFragment_to_graphDetailFragment
id action_historyFragment_to_graphFragment
id action_historyFragment_to_historyDetailFragment
id action_last_month
id action_last_quarter
id action_last_week
id action_last_year
id action_loginFragment_to_homeFragment
id action_loginFragment_to_registerFragment
id action_navigation_aufgaben_to_konzentrationFragment
id action_navigation_aufgaben_to_manage_tasks
id action_navigation_aufgaben_to_taskHistoryFragment
id action_navigation_ergebniserfassung_to_breakbuildingFragment
id action_navigation_ergebniserfassung_to_exercise_history
id action_navigation_ergebniserfassung_to_pottingFragment
id action_navigation_ergebniserfassung_to_safetiesFragment
id action_navigation_ergebniserfassung_to_splitsFragment
id action_navigation_ergebniserfassung_to_stellungsspielFragment
id action_navigation_ergebniserfassung_to_technikFragment
id action_navigation_exercise_history_to_exercise_history_detail
id action_navigation_trainingsplan_to_exerciseSelectionFragment
id action_navigation_trainingsplan_to_manage_exercises
id action_navigation_trainingsplan_to_trainingsplanHistoryFragment
id action_pottingFragment_to_exerciseDetailFragment
id action_registerFragment_to_homeFragment
id action_safetiesFragment_to_exerciseDetailFragment
id action_selbsteinschaetzungFragment_to_historyFragment
id action_settings_to_login
id action_settings_to_userManagement
id action_splitsFragment_to_exerciseDetailFragment
id action_stellungsspielFragment_to_exerciseDetailFragment
id action_technikFragment_to_exerciseDetailFragment
id addButton
id addConnectionCard
id addExerciseButton
id addExerciseFab
id addScore10Button
id addScore3Button
id addScore6Button
id addScoreButton
id addTaskFab
id addTimeButton
id addTrainerButton
id afterTournamentFragment
id afterTrainingFragment
id answerText
id aufgabentechnikFragment
id auth_nav_graph
id averageTimeText
id beforeTournamentFragment
id beforeTrainingFragment
id blueThemePreview
id blueThemePreviewInclude
id blueThemeRadioButton
id breakbuildingFragment
id button_manual_entry_revised
id button_toggle_timer
id cancelButton
id cardView
id categoryCard
id categoryHeaderExpandIcon
id categoryHeaderNameTextView
id categoryName
id categoryNameText
id categoryNameTextView
id categoryTabs
id categoryTasksFragment
id categoryText
id categoryTitleText
id categoryTitleTextView
id checkBox
id checkboxContainer
id colorIndicator
id completionCountText
id completionStatsTextView
id completionText
id confirmPasswordEditText
id confirmPasswordInputLayout
id connectionsLabel
id connectionsRecyclerView
id container
id contentContainer
id contentLayout
id contentText
id countControls
id countText
id crimsonThemePreview
id crimsonThemePreviewInclude
id crimsonThemeRadioButton
id currentColumnText
id customExerciseLayout
id darkBlueThemePreview
id darkBlueThemePreviewInclude
id darkBlueThemeRadioButton
id darkThemePreview
id darkThemePreviewInclude
id darkThemeRadioButton
id dateRangeText
id dateText
id datesRecyclerView
id debugButton
id decreaseButton
id deleteButton
id deleteExerciseButton
id deleteTaskButton
id descriptionText
id dialogTitle
id editButton
id editExerciseButton
id editTaskButton
id edittext_manual_hours
id edittext_manual_minutes
id emailEditText
id emailInputLayout
id emailLabel
id emailTextView
id emptyHistoryText
id emptyStateText
id emptyStateTextView
id emptyView
id emptyViewTasks
id endDateButton
id entriesCountText
id exerciseAccessCheckbox
id exerciseCategoryAutoCompleteTextView
id exerciseCategoryInputLayout
id exerciseCategoryTextView
id exerciseCheckbox
id exerciseDescriptionEditText
id exerciseDescriptionInputLayout
id exerciseDetailFragment
id exerciseGraphFragment
id exerciseHistoryDetailFragment
id exerciseName
id exerciseNameEditText
id exerciseNameInput
id exerciseNameInputLayout
id exerciseNameLayout
id exerciseNameTextView
id exerciseRecyclerView
id exerciseSelectionFragment
id exerciseType
id exerciseTypeAutoCompleteTextView
id exerciseTypeDropdown
id exerciseTypeInputLayout
id exerciseTypeLayout
id exercisesRecyclerView
id expandCollapseIcon
id forgotPasswordButton
id graphButton
id graphContainer
id graphDetailFragment
id graphFragment
id headerLayout
id historyButton
id historyCard
id historyDetailFragment
id historyFragment
id historyRecyclerView
id historyTitleText
id increaseButton
id itemsTextView
id konzentrationFragment
id lastScoreText
id lastSyncLabel
id lastSyncTextView
id lastTimeText
id legendButton
id lightThemePreview
id lightThemePreviewInclude
id lightThemeRadioButton
id lineChart
id loginButton
id loginFragment
id logoutButton
id manageExercisesButton
id manageExercisesTitleTextView
id manageTasksButton
id manageTasksTitleTextView
id missButton
id mobile_navigation
id monthCardView
id monthText
id nameEditText
id nameInputLayout
id nameTextView
id nav_graph
id nav_host_fragment
id nav_view
id navigation_aufgaben
id navigation_ergebniserfassung
id navigation_exercise_history
id navigation_exercise_history_detail
id navigation_generic_exercise_list
id navigation_home
id navigation_manage_exercises
id navigation_manage_tasks
id navigation_selbsteinschaetzung
id navigation_settings
id navigation_task_history
id navigation_trainingsplan
id neonThemePreview
id neonThemePreviewInclude
id neonThemeRadioButton
id newCategoryEditText
id newCategoryInputLayout
id newCategoryNameEditText
id newCategoryNameInputLayout
id oceanThemePreview
id oceanThemePreviewInclude
id oceanThemeRadioButton
id openWebButton
id passwordEditText
id passwordInputLayout
id permissionsLabel
id permissionsTextView
id pointsPicker
id pointsPickerLayout
id pointsText
id pointsTextView
id pottingFragment
id progressBar
id questionsAfterTournamentFragment
id questionsAfterTrainingFragment
id questionsBeforeTournamentFragment
id questionsBeforeTrainingFragment
id questionsFragment
id recyclerView
id registerButton
id registerFragment
id removeButton
id roleDropdown
id roleInputLayout
id roleTextView
id safetiesFragment
id saveButton
id scoreLabel
id scoreName
id scoreSlider
id scoreText
id scores10Text
id scores1Text
id scores2Text
id scores3Text
id scores4Text
id scores5Text
id scores6Text
id selectedPointsText
id selfAssessmentAccessCheckbox
id showGraphButton
id snookerThemePreview
id snookerThemePreviewInclude
id snookerThemeRadioButton
id splitsFragment
id startDateButton
id statusChip
id stellungsspielFragment
id successButton
id syncCard
id syncNowButton
id syncResultLabel
id syncResultTextView
id syncStatusLabel
id syncStatusTextView
id systemThemeRadioButton
id targetCountLayout
id targetCountPicker
id taskCard
id taskCategoriesRecyclerView
id taskCategoryAutoCompleteTextView
id taskCategoryInputLayout
id taskCategoryTextView
id taskCheckbox
id taskCompletionsRecyclerView
id taskDescription
id taskDescriptionEditText
id taskDescriptionInputLayout
id taskFrequency
id taskFrequencyAutoCompleteTextView
id taskFrequencyInputLayout
id taskFrequencyTextView
id taskHistoryFragment
id taskPoints
id taskPointsEditText
id taskPointsInputLayout
id taskPointsTextView
id taskTitle
id taskTitleEditText
id taskTitleInputLayout
id taskTitleText
id taskTitleTextView
id taskWeeklyFrequencyCountEditText
id taskWeeklyFrequencyCountLayout
id tasksRecyclerView
id technikFragment
id textview_weekly_training_time
id themeCard
id themeRadioGroup
id timeText
id timeframeButton
id timer_controls_layout
id titleText
id titleTextView
id totalPointsText
id totalTimeText
id trainerTypeDropdown
id trainerTypeInputLayout
id training_chronometer
id trainingsplanHistoryFragment
id trainingsplanHistoryRecyclerView
id trainingsplanRecyclerView
id tvContent
id typeText
id userManagementButton
id userManagementCard
id userManagementFragment
id webAccessCard
id weekText
id weekTextView
layout activity_main
layout dialog_add_edit_exercise
layout dialog_add_edit_task
layout dialog_add_exercise
layout dialog_custom_exercise
layout dialog_custom_timeframe
layout dialog_edit_connection
layout dialog_exercise_selection
layout dialog_legend
layout dialog_manual_time_entry
layout dialog_select_points
layout dropdown_item
layout exercise_selection_item
layout fragment_after_tournament
layout fragment_after_training
layout fragment_aufgaben
layout fragment_before_tournament
layout fragment_before_training
layout fragment_breakbuilding
layout fragment_ergebniserfassung
layout fragment_exercise_detail
layout fragment_exercise_detail_splits
layout fragment_exercise_detail_stellungsspiel
layout fragment_exercise_detail_timeonly
layout fragment_exercise_graph
layout fragment_exercise_history
layout fragment_exercise_history_detail
layout fragment_exercise_selection
layout fragment_generic_exercise_list
layout fragment_graph
layout fragment_graph_detail
layout fragment_history
layout fragment_history_detail
layout fragment_login
layout fragment_manage_exercises
layout fragment_manage_tasks
layout fragment_potting
layout fragment_profile
layout fragment_questions
layout fragment_questions_after_tournament
layout fragment_questions_after_training
layout fragment_questions_before_tournament
layout fragment_questions_before_training
layout fragment_register
layout fragment_safeties
layout fragment_selbsteinschaetzung
layout fragment_settings
layout fragment_splits
layout fragment_stellungsspiel
layout fragment_task_history
layout fragment_task_list
layout fragment_technik
layout fragment_trainingsplan
layout fragment_trainingsplan_history
layout fragment_user_management
layout item_assessment
layout item_breakbuilding
layout item_date
layout item_exercise
layout item_exercise_category
layout item_exercise_definition
layout item_exercise_history
layout item_history
layout item_history_detail
layout item_legend
layout item_manage_exercise_category_header
layout item_manage_task
layout item_manage_task_category_header
layout item_month_header
layout item_question
layout item_task
layout item_task_category
layout item_task_category_header
layout item_task_completion
layout item_task_completion_header
layout item_task_history
layout item_training_assessment
layout item_user_connection
layout marker_view
layout theme_preview_blue
layout theme_preview_crimson
layout theme_preview_dark
layout theme_preview_dark_blue
layout theme_preview_light
layout theme_preview_neon
layout theme_preview_ocean
layout theme_preview_snooker
layout trainingsplan_history_item
layout trainingsplan_item
menu bottom_nav_menu
menu menu_history
menu menu_manage_exercises
menu menu_timeframe
menu menu_timeframe_simple
mipmap ic_launcher
mipmap ic_launcher_background
mipmap ic_launcher_foreground
mipmap ic_launcher_round
navigation auth_nav_graph
navigation mobile_navigation
navigation nav_graph
string action_create_new_category
string add_exercise_button_label
string add_task_dialog_title
string app_name
string button_cancel
string button_delete
string button_save
string cancel_button
string category_not_specified_toast
string confirm_delete_exercise_message
string confirm_remove_exercise_from_plan_message
string content_desc_add_new_task_fab
string create_new_category_option
string debug_menu_title
string default_exercises_added_toast
string default_exercises_migrated_toast
string default_exercises_migration_failed_toast
string default_exercises_partial_migration_toast
string delete_button
string delete_task_dialog_message
string delete_task_dialog_title
string dialog_title_add_exercise
string dialog_title_confirmation
string dialog_title_delete_exercise
string dialog_title_edit_exercise
string edit_button
string edit_task_dialog_title
string error_deleting_exercise_toast
string error_loading_categories_toast
string error_loading_exercises_toast
string error_saving_exercise_toast
string error_user_id_not_found
string error_user_id_not_found_info
string exercise_deleted_toast
string exercise_not_deletable_toast
string exercise_saved_toast
string exercise_type_normal
string exercise_type_splits
string exercise_type_stellungsspiel
string exercise_type_timeonly
string fragment_label_manage_exercises
string gcm_defaultSenderId
string google_api_key
string google_app_id
string google_crash_reporting_api_key
string google_storage_bucket
string hint_new_category_name
string hint_task_category
string hint_task_description_optional
string hint_task_frequency
string hint_task_points
string hint_task_times_per_week
string hint_task_title
string input_error_category_required
string input_error_name_required
string label_category
string label_description
string label_empty_exercise_list
string label_exercise_type
string label_name
string label_new_category
string manage_my_exercises_button
string manage_tasks_button_text
string manage_tasks_fragment_title
string no
string no_categories_found_toast
string no_exercises_found_add_some_toast
string no_exercises_in_category_generic
string no_exercises_in_category_toast
string no_tasks_yet
string please_log_in_to_load_categories_toast
string please_log_in_to_load_exercises_toast
string please_log_in_to_manage_exercises_toast
string project_id
string save_button
string title_category_exercises_generic
string title_manage_exercises
string toast_category_info_invalid
string toast_invalid_frequency_selected
string toast_invalid_points_value
string toast_no_specific_view_for_category
string toast_notification_forced
string toast_notification_sent_none
string toast_notification_sent_success
string toast_plan_reset_archived
string toast_please_select_exercise
string toast_reminder_rescheduled
string toast_required_fields_missing
string toast_task_added
string toast_task_deleted
string toast_task_updated
string unknown_category
string yes
style BlueCardStyle
style BlueCardStyleDark
style BlueNegativeButtonStyle
style BlueNegativeButtonStyle.Night
style BluePositiveButtonStyle
style BluePositiveButtonStyle.Night
style CrimsonCardStyle
style CrimsonCardStyleDark
style CrimsonNegativeButtonStyle
style CrimsonNegativeButtonStyle.Night
style CrimsonPositiveButtonStyle
style CrimsonPositiveButtonStyle.Night
style CustomDropdownStyle
style CustomDropdownStyle.Blue
style CustomDropdownStyle.Crimson
style CustomDropdownStyle.Dark
style CustomDropdownStyle.DarkBlue
style CustomDropdownStyle.Neon
style CustomDropdownStyle.Ocean
style CustomDropdownStyle.Snooker
style CustomDropdownTextInputLayout
style CustomDropdownTextInputLayout.Blue
style CustomDropdownTextInputLayout.Crimson
style CustomDropdownTextInputLayout.Dark
style CustomDropdownTextInputLayout.DarkBlue
style CustomDropdownTextInputLayout.Neon
style CustomDropdownTextInputLayout.Ocean
style CustomDropdownTextInputLayout.Snooker
style CustomTabLayout
style CustomTabLayout.Blue
style CustomTabLayout.Crimson
style CustomTabLayout.Dark
style CustomTabLayout.DarkBlue
style CustomTabLayout.Neon
style CustomTabLayout.Ocean
style CustomTabLayout.Snooker
style CustomTextInputLayout
style CustomTextInputLayout.Blue
style CustomTextInputLayout.Crimson
style CustomTextInputLayout.Dark
style CustomTextInputLayout.DarkBlue
style CustomTextInputLayout.Neon
style CustomTextInputLayout.Ocean
style CustomTextInputLayout.Snooker
style DarkBlueCardStyle
style DarkBlueCardStyleDark
style DarkBlueNegativeButtonStyle
style DarkBlueNegativeButtonStyle.Night
style DarkBluePositiveButtonStyle
style DarkBluePositiveButtonStyle.Night
style DarkNegativeButtonStyle
style DarkPositiveButtonStyle
style DatePickerButtonStyle
style DatePickerDialogTheme
style DefaultAnimationDialog
style DefaultAnimationDialog.Blue
style DefaultAnimationDialog.Crimson
style DefaultAnimationDialog.Dark
style DefaultAnimationDialog.DarkBlue
style DefaultAnimationDialog.Neon
style DefaultAnimationDialog.Ocean
style DefaultAnimationDialog.Snooker
style DropdownAnimation
style DropdownDialog
style DropdownDialog.Blue
style DropdownDialog.Crimson
style DropdownDialog.Dark
style DropdownDialog.DarkBlue
style DropdownDialog.Neon
style DropdownDialog.Ocean
style DropdownDialog.Snooker
style NeonCardStyle
style NeonCardStyleDark
style NeonNegativeButtonStyle
style NeonNegativeButtonStyle.Night
style NeonPositiveButtonStyle
style NeonPositiveButtonStyle.Night
style OceanCardStyle
style OceanCardStyleDark
style OceanNegativeButtonStyle
style OceanNegativeButtonStyle.Night
style OceanPositiveButtonStyle
style OceanPositiveButtonStyle.Night
style PopupMenuItemStyle
style PopupMenuItemStyle_Blue
style PopupMenuItemStyle_Dark
style PopupMenuItemStyle_DarkBlue
style PopupMenuItemStyle_Snooker
style PopupMenuListViewStyle
style PopupMenuListViewStyle_Blue
style PopupMenuListViewStyle_Crimson
style PopupMenuListViewStyle_Dark
style PopupMenuListViewStyle_DarkBlue
style PopupMenuListViewStyle_Neon
style PopupMenuListViewStyle_Ocean
style PopupMenuListViewStyle_Snooker
style PopupMenuStyle
style PopupMenuStyle_Blue
style PopupMenuStyle_Crimson
style PopupMenuStyle_Dark
style PopupMenuStyle_DarkBlue
style PopupMenuStyle_Neon
style PopupMenuStyle_Ocean
style PopupMenuStyle_Snooker
style SnookerCardStyle
style SnookerCardStyleDark
style SnookerNegativeButtonStyle
style SnookerNegativeButtonStyle.Night
style SnookerPositiveButtonStyle
style SnookerPositiveButtonStyle.Night
style Theme.DieSnookerApp
style Theme.DieSnookerApp.Blue
style Theme.DieSnookerApp.Crimson
style Theme.DieSnookerApp.DarkBlue
style Theme.DieSnookerApp.Neon
style Theme.DieSnookerApp.Ocean
style Theme.DieSnookerApp.Snooker
xml backup_rules
xml data_extraction_rules
