// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ExerciseSelectionItemBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final LinearLayout countControls;

  @NonNull
  public final TextView countText;

  @NonNull
  public final ImageButton decreaseButton;

  @NonNull
  public final CheckBox exerciseCheckbox;

  @NonNull
  public final ImageButton increaseButton;

  private ExerciseSelectionItemBinding(@NonNull MaterialCardView rootView,
      @NonNull LinearLayout countControls, @NonNull TextView countText,
      @NonNull ImageButton decreaseButton, @NonNull CheckBox exerciseCheckbox,
      @NonNull ImageButton increaseButton) {
    this.rootView = rootView;
    this.countControls = countControls;
    this.countText = countText;
    this.decreaseButton = decreaseButton;
    this.exerciseCheckbox = exerciseCheckbox;
    this.increaseButton = increaseButton;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ExerciseSelectionItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ExerciseSelectionItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.exercise_selection_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ExerciseSelectionItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.countControls;
      LinearLayout countControls = ViewBindings.findChildViewById(rootView, id);
      if (countControls == null) {
        break missingId;
      }

      id = R.id.countText;
      TextView countText = ViewBindings.findChildViewById(rootView, id);
      if (countText == null) {
        break missingId;
      }

      id = R.id.decreaseButton;
      ImageButton decreaseButton = ViewBindings.findChildViewById(rootView, id);
      if (decreaseButton == null) {
        break missingId;
      }

      id = R.id.exerciseCheckbox;
      CheckBox exerciseCheckbox = ViewBindings.findChildViewById(rootView, id);
      if (exerciseCheckbox == null) {
        break missingId;
      }

      id = R.id.increaseButton;
      ImageButton increaseButton = ViewBindings.findChildViewById(rootView, id);
      if (increaseButton == null) {
        break missingId;
      }

      return new ExerciseSelectionItemBinding((MaterialCardView) rootView, countControls, countText,
          decreaseButton, exerciseCheckbox, increaseButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
