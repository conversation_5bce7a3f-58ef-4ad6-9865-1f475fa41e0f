// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class TrainingsplanItemBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final CheckBox checkBox;

  @NonNull
  public final TextView completionCountText;

  @NonNull
  public final ImageButton deleteButton;

  @NonNull
  public final TextView exerciseName;

  @NonNull
  public final TextView exerciseType;

  private TrainingsplanItemBinding(@NonNull MaterialCardView rootView, @NonNull CheckBox checkBox,
      @NonNull TextView completionCountText, @NonNull ImageButton deleteButton,
      @NonNull TextView exerciseName, @NonNull TextView exerciseType) {
    this.rootView = rootView;
    this.checkBox = checkBox;
    this.completionCountText = completionCountText;
    this.deleteButton = deleteButton;
    this.exerciseName = exerciseName;
    this.exerciseType = exerciseType;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static TrainingsplanItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static TrainingsplanItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.trainingsplan_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static TrainingsplanItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.checkBox;
      CheckBox checkBox = ViewBindings.findChildViewById(rootView, id);
      if (checkBox == null) {
        break missingId;
      }

      id = R.id.completionCountText;
      TextView completionCountText = ViewBindings.findChildViewById(rootView, id);
      if (completionCountText == null) {
        break missingId;
      }

      id = R.id.deleteButton;
      ImageButton deleteButton = ViewBindings.findChildViewById(rootView, id);
      if (deleteButton == null) {
        break missingId;
      }

      id = R.id.exerciseName;
      TextView exerciseName = ViewBindings.findChildViewById(rootView, id);
      if (exerciseName == null) {
        break missingId;
      }

      id = R.id.exerciseType;
      TextView exerciseType = ViewBindings.findChildViewById(rootView, id);
      if (exerciseType == null) {
        break missingId;
      }

      return new TrainingsplanItemBinding((MaterialCardView) rootView, checkBox,
          completionCountText, deleteButton, exerciseName, exerciseType);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
