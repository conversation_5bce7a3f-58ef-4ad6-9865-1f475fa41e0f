// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.github.mikephil.charting.charts.LineChart;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentGraphDetailBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton legendButton;

  @NonNull
  public final LineChart lineChart;

  @NonNull
  public final MaterialButton timeframeButton;

  @NonNull
  public final TextView titleText;

  private FragmentGraphDetailBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton legendButton, @NonNull LineChart lineChart,
      @NonNull MaterialButton timeframeButton, @NonNull TextView titleText) {
    this.rootView = rootView;
    this.legendButton = legendButton;
    this.lineChart = lineChart;
    this.timeframeButton = timeframeButton;
    this.titleText = titleText;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentGraphDetailBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentGraphDetailBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_graph_detail, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentGraphDetailBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.legendButton;
      MaterialButton legendButton = ViewBindings.findChildViewById(rootView, id);
      if (legendButton == null) {
        break missingId;
      }

      id = R.id.lineChart;
      LineChart lineChart = ViewBindings.findChildViewById(rootView, id);
      if (lineChart == null) {
        break missingId;
      }

      id = R.id.timeframeButton;
      MaterialButton timeframeButton = ViewBindings.findChildViewById(rootView, id);
      if (timeframeButton == null) {
        break missingId;
      }

      id = R.id.titleText;
      TextView titleText = ViewBindings.findChildViewById(rootView, id);
      if (titleText == null) {
        break missingId;
      }

      return new FragmentGraphDetailBinding((ConstraintLayout) rootView, legendButton, lineChart,
          timeframeButton, titleText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
