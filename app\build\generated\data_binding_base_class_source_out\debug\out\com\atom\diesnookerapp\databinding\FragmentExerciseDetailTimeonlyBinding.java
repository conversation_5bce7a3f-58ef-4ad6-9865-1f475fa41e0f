// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentExerciseDetailTimeonlyBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton addTimeButton;

  @NonNull
  public final TextView lastTimeText;

  @NonNull
  public final TextView titleText;

  @NonNull
  public final TextView totalTimeText;

  private FragmentExerciseDetailTimeonlyBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton addTimeButton, @NonNull TextView lastTimeText,
      @NonNull TextView titleText, @NonNull TextView totalTimeText) {
    this.rootView = rootView;
    this.addTimeButton = addTimeButton;
    this.lastTimeText = lastTimeText;
    this.titleText = titleText;
    this.totalTimeText = totalTimeText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentExerciseDetailTimeonlyBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentExerciseDetailTimeonlyBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_exercise_detail_timeonly, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentExerciseDetailTimeonlyBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addTimeButton;
      MaterialButton addTimeButton = ViewBindings.findChildViewById(rootView, id);
      if (addTimeButton == null) {
        break missingId;
      }

      id = R.id.lastTimeText;
      TextView lastTimeText = ViewBindings.findChildViewById(rootView, id);
      if (lastTimeText == null) {
        break missingId;
      }

      id = R.id.titleText;
      TextView titleText = ViewBindings.findChildViewById(rootView, id);
      if (titleText == null) {
        break missingId;
      }

      id = R.id.totalTimeText;
      TextView totalTimeText = ViewBindings.findChildViewById(rootView, id);
      if (totalTimeText == null) {
        break missingId;
      }

      return new FragmentExerciseDetailTimeonlyBinding((LinearLayout) rootView, addTimeButton,
          lastTimeText, titleText, totalTimeText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
