1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.atom.diesnookerapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Permission for notifications -->
12    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
12-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:6:5-77
12-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:6:22-74
13    <!-- Permission for scheduling work -->
14    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
14-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:8:5-81
14-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:8:22-78
15    <!-- Permission for internet access -->
16    <uses-permission android:name="android.permission.INTERNET" />
16-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:10:5-67
16-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:10:22-64
17    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
17-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:26:5-79
17-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:26:22-76
18    <uses-permission android:name="android.permission.WAKE_LOCK" />
18-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:23:5-68
18-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:23:22-65
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
19-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:26:5-77
19-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:26:22-74
20
21    <permission
21-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960c7054b802f15a0b75b85e164cffa8\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
22        android:name="com.atom.diesnookerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
22-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960c7054b802f15a0b75b85e164cffa8\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
23        android:protectionLevel="signature" />
23-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960c7054b802f15a0b75b85e164cffa8\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
24
25    <uses-permission android:name="com.atom.diesnookerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
25-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960c7054b802f15a0b75b85e164cffa8\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
25-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960c7054b802f15a0b75b85e164cffa8\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
26
27    <application
27-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:12:5-45:19
28        android:name="com.atom.diesnookerapp.SnookerApplication"
28-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:13:9-43
29        android:allowBackup="true"
29-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:14:9-35
30        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
30-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960c7054b802f15a0b75b85e164cffa8\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
31        android:dataExtractionRules="@xml/data_extraction_rules"
31-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:15:9-65
32        android:debuggable="true"
33        android:extractNativeLibs="false"
34        android:fullBackupContent="@xml/backup_rules"
34-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:16:9-54
35        android:icon="@mipmap/ic_launcher"
35-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:17:9-43
36        android:label="@string/app_name"
36-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:18:9-41
37        android:roundIcon="@mipmap/ic_launcher_round"
37-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:19:9-54
38        android:supportsRtl="true"
38-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:20:9-35
39        android:testOnly="true"
40        android:theme="@style/Theme.DieSnookerApp" >
40-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:21:9-51
41        <activity
41-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:23:9-33:20
42            android:name="com.atom.diesnookerapp.MainActivity"
42-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:24:13-41
43            android:exported="true"
43-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:25:13-36
44            android:label="@string/app_name"
44-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:26:13-45
45            android:theme="@style/Theme.DieSnookerApp" >
45-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:27:13-55
46            <intent-filter>
46-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:28:13-32:29
47                <action android:name="android.intent.action.MAIN" />
47-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:29:17-69
47-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:29:25-66
48
49                <category android:name="android.intent.category.LAUNCHER" />
49-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:31:17-77
49-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:31:27-74
50            </intent-filter>
51        </activity>
52
53        <!-- Receiver for WorkManager to restart scheduled jobs after device reboot -->
54        <receiver
54-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:36:9-44:20
55            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
55-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:37:13-88
56            android:directBootAware="false"
56-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:112:13-44
57            android:enabled="false"
57-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:113:13-36
58            android:exported="false" >
58-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:38:13-37
59            <intent-filter>
59-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:39:13-43:29
60                <action android:name="android.intent.action.BOOT_COMPLETED" />
60-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:40:17-79
60-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:40:25-76
61                <action android:name="android.intent.action.TIME_SET" />
61-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:41:17-73
61-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:41:25-70
62                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
62-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:42:17-81
62-->D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:42:25-78
63            </intent-filter>
64        </receiver>
65
66        <activity
66-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4d885512c0beef2ec8aea2204c9f1c7\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
67            android:name="androidx.compose.ui.tooling.PreviewActivity"
67-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4d885512c0beef2ec8aea2204c9f1c7\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
68            android:exported="true" />
68-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4d885512c0beef2ec8aea2204c9f1c7\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
69        <activity
69-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f80cdb95ae6a2948d9ffde64c419845\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:23:9-25:39
70            android:name="androidx.activity.ComponentActivity"
70-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f80cdb95ae6a2948d9ffde64c419845\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:24:13-63
71            android:exported="true" />
71-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f80cdb95ae6a2948d9ffde64c419845\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:25:13-36
72
73        <service
73-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f1c5947f95698f6a281d724a14c88cf\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:8:9-14:19
74            android:name="com.google.firebase.components.ComponentDiscoveryService"
74-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f1c5947f95698f6a281d724a14c88cf\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:9:13-84
75            android:directBootAware="true"
75-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
76            android:exported="false" >
76-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f1c5947f95698f6a281d724a14c88cf\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:10:13-37
77            <meta-data
77-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f1c5947f95698f6a281d724a14c88cf\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:11:13-13:85
78                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
78-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f1c5947f95698f6a281d724a14c88cf\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:12:17-119
79                android:value="com.google.firebase.components.ComponentRegistrar" />
79-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f1c5947f95698f6a281d724a14c88cf\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:13:17-82
80            <meta-data
80-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:69:13-71:85
81                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
81-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:70:17-109
82                android:value="com.google.firebase.components.ComponentRegistrar" />
82-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:71:17-82
83            <meta-data
83-->[com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c176bc85a783f4b95615ae4634f8ae2f\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:12:13-14:85
84                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
84-->[com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c176bc85a783f4b95615ae4634f8ae2f\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:13:17-129
85                android:value="com.google.firebase.components.ComponentRegistrar" />
85-->[com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c176bc85a783f4b95615ae4634f8ae2f\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:14:17-82
86            <meta-data
86-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c4f5c5e1a3cc63e3269d5bb878123ce\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:17:13-19:85
87                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
87-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c4f5c5e1a3cc63e3269d5bb878123ce\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:18:17-122
88                android:value="com.google.firebase.components.ComponentRegistrar" />
88-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c4f5c5e1a3cc63e3269d5bb878123ce\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:19:17-82
89            <meta-data
89-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c4f5c5e1a3cc63e3269d5bb878123ce\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:20:13-22:85
90                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
90-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c4f5c5e1a3cc63e3269d5bb878123ce\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:21:17-111
91                android:value="com.google.firebase.components.ComponentRegistrar" />
91-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c4f5c5e1a3cc63e3269d5bb878123ce\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:22:17-82
92            <meta-data
92-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d8af47904ad7f9485d00657e6341c9f\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
93                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
93-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d8af47904ad7f9485d00657e6341c9f\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
94                android:value="com.google.firebase.components.ComponentRegistrar" />
94-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d8af47904ad7f9485d00657e6341c9f\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
95            <meta-data
95-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
96                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
96-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
97                android:value="com.google.firebase.components.ComponentRegistrar" />
97-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
98        </service>
99
100        <activity
100-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:29:9-46:20
101            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
101-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:30:13-80
102            android:excludeFromRecents="true"
102-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:31:13-46
103            android:exported="true"
103-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:32:13-36
104            android:launchMode="singleTask"
104-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:33:13-44
105            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
105-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:34:13-72
106            <intent-filter>
106-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:35:13-45:29
107                <action android:name="android.intent.action.VIEW" />
107-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:17-69
107-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:25-66
108
109                <category android:name="android.intent.category.DEFAULT" />
109-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:17-76
109-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:27-73
110                <category android:name="android.intent.category.BROWSABLE" />
110-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:17-78
110-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:27-75
111
112                <data
112-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:41:17-44:51
113                    android:host="firebase.auth"
113-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:42:21-49
114                    android:path="/"
114-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:43:21-37
115                    android:scheme="genericidp" />
115-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:44:21-48
116            </intent-filter>
117        </activity>
118        <activity
118-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:47:9-64:20
119            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
119-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:48:13-79
120            android:excludeFromRecents="true"
120-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:49:13-46
121            android:exported="true"
121-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:50:13-36
122            android:launchMode="singleTask"
122-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:51:13-44
123            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
123-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:52:13-72
124            <intent-filter>
124-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:53:13-63:29
125                <action android:name="android.intent.action.VIEW" />
125-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:17-69
125-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:25-66
126
127                <category android:name="android.intent.category.DEFAULT" />
127-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:17-76
127-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:27-73
128                <category android:name="android.intent.category.BROWSABLE" />
128-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:17-78
128-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:27-75
129
130                <data
130-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:41:17-44:51
131                    android:host="firebase.auth"
131-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:42:21-49
132                    android:path="/"
132-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:43:21-37
133                    android:scheme="recaptcha" />
133-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:44:21-48
134            </intent-filter>
135        </activity>
136
137        <provider
137-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
138            android:name="com.google.firebase.provider.FirebaseInitProvider"
138-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
139            android:authorities="com.atom.diesnookerapp.firebaseinitprovider"
139-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
140            android:directBootAware="true"
140-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
141            android:exported="false"
141-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
142            android:initOrder="100" />
142-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
143
144        <activity
144-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f74f887e8617a184c260b963fe8585b\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
145            android:name="com.google.android.gms.common.api.GoogleApiActivity"
145-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f74f887e8617a184c260b963fe8585b\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
146            android:exported="false"
146-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f74f887e8617a184c260b963fe8585b\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
147            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
147-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f74f887e8617a184c260b963fe8585b\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
148
149        <provider
149-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:29:9-37:20
150            android:name="androidx.startup.InitializationProvider"
150-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:30:13-67
151            android:authorities="com.atom.diesnookerapp.androidx-startup"
151-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:31:13-68
152            android:exported="false" >
152-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:32:13-37
153            <meta-data
153-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:34:13-36:52
154                android:name="androidx.work.WorkManagerInitializer"
154-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:35:17-68
155                android:value="androidx.startup" />
155-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:36:17-49
156            <meta-data
156-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2daf4fa1f3706cd82523bfe6089ca9b2\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
157                android:name="androidx.emoji2.text.EmojiCompatInitializer"
157-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2daf4fa1f3706cd82523bfe6089ca9b2\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
158                android:value="androidx.startup" />
158-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2daf4fa1f3706cd82523bfe6089ca9b2\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
159            <meta-data
159-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f06ba6d15bb51653c2986f9faea87ba3\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
160                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
160-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f06ba6d15bb51653c2986f9faea87ba3\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
161                android:value="androidx.startup" />
161-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f06ba6d15bb51653c2986f9faea87ba3\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
162            <meta-data
162-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
163                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
163-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
164                android:value="androidx.startup" />
164-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
165        </provider>
166
167        <service
167-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:39:9-45:35
168            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
168-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:40:13-88
169            android:directBootAware="false"
169-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:41:13-44
170            android:enabled="@bool/enable_system_alarm_service_default"
170-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:42:13-72
171            android:exported="false" />
171-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:43:13-37
172        <service
172-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:46:9-52:35
173            android:name="androidx.work.impl.background.systemjob.SystemJobService"
173-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:47:13-84
174            android:directBootAware="false"
174-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:48:13-44
175            android:enabled="@bool/enable_system_job_service_default"
175-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:49:13-70
176            android:exported="true"
176-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:50:13-36
177            android:permission="android.permission.BIND_JOB_SERVICE" />
177-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:51:13-69
178        <service
178-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:53:9-59:35
179            android:name="androidx.work.impl.foreground.SystemForegroundService"
179-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:54:13-81
180            android:directBootAware="false"
180-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:55:13-44
181            android:enabled="@bool/enable_system_foreground_service_default"
181-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:56:13-77
182            android:exported="false" />
182-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:57:13-37
183
184        <receiver
184-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:61:9-66:35
185            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
185-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:62:13-88
186            android:directBootAware="false"
186-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:63:13-44
187            android:enabled="true"
187-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:64:13-35
188            android:exported="false" />
188-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:65:13-37
189        <receiver
189-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:67:9-77:20
190            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
190-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:68:13-106
191            android:directBootAware="false"
191-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:69:13-44
192            android:enabled="false"
192-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:70:13-36
193            android:exported="false" >
193-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:71:13-37
194            <intent-filter>
194-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:73:13-76:29
195                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
195-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:74:17-87
195-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:74:25-84
196                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
196-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:75:17-90
196-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:75:25-87
197            </intent-filter>
198        </receiver>
199        <receiver
199-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:78:9-88:20
200            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
200-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:79:13-104
201            android:directBootAware="false"
201-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:80:13-44
202            android:enabled="false"
202-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:81:13-36
203            android:exported="false" >
203-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:82:13-37
204            <intent-filter>
204-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:84:13-87:29
205                <action android:name="android.intent.action.BATTERY_OKAY" />
205-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:85:17-77
205-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:85:25-74
206                <action android:name="android.intent.action.BATTERY_LOW" />
206-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:86:17-76
206-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:86:25-73
207            </intent-filter>
208        </receiver>
209        <receiver
209-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:89:9-99:20
210            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
210-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:90:13-104
211            android:directBootAware="false"
211-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:91:13-44
212            android:enabled="false"
212-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:92:13-36
213            android:exported="false" >
213-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:93:13-37
214            <intent-filter>
214-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:95:13-98:29
215                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
215-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:96:17-83
215-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:96:25-80
216                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
216-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:97:17-82
216-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:97:25-79
217            </intent-filter>
218        </receiver>
219        <receiver
219-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:100:9-109:20
220            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
220-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:101:13-103
221            android:directBootAware="false"
221-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:102:13-44
222            android:enabled="false"
222-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:103:13-36
223            android:exported="false" >
223-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:104:13-37
224            <intent-filter>
224-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:106:13-108:29
225                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
225-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:107:17-79
225-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:107:25-76
226            </intent-filter>
227        </receiver>
228        <receiver
228-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:122:9-131:20
229            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
229-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:123:13-99
230            android:directBootAware="false"
230-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:124:13-44
231            android:enabled="@bool/enable_system_alarm_service_default"
231-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:125:13-72
232            android:exported="false" >
232-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:126:13-37
233            <intent-filter>
233-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:128:13-130:29
234                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
234-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:129:17-98
234-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:129:25-95
235            </intent-filter>
236        </receiver>
237        <receiver
237-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:132:9-142:20
238            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
238-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:133:13-78
239            android:directBootAware="false"
239-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:134:13-44
240            android:enabled="true"
240-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:135:13-35
241            android:exported="true"
241-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:136:13-36
242            android:permission="android.permission.DUMP" >
242-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:137:13-57
243            <intent-filter>
243-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:139:13-141:29
244                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
244-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:140:17-88
244-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:140:25-85
245            </intent-filter>
246        </receiver>
247
248        <uses-library
248-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91a05db1f70fcc1560e99a1a2e5a1e0b\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
249            android:name="androidx.window.extensions"
249-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91a05db1f70fcc1560e99a1a2e5a1e0b\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
250            android:required="false" />
250-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91a05db1f70fcc1560e99a1a2e5a1e0b\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
251        <uses-library
251-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91a05db1f70fcc1560e99a1a2e5a1e0b\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
252            android:name="androidx.window.sidecar"
252-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91a05db1f70fcc1560e99a1a2e5a1e0b\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
253            android:required="false" />
253-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91a05db1f70fcc1560e99a1a2e5a1e0b\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
254
255        <service
255-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a5b100d33267656477d10d382e016a1\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
256            android:name="androidx.room.MultiInstanceInvalidationService"
256-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a5b100d33267656477d10d382e016a1\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
257            android:directBootAware="true"
257-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a5b100d33267656477d10d382e016a1\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
258            android:exported="false" />
258-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a5b100d33267656477d10d382e016a1\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
259
260        <meta-data
260-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92e8c801482673d28198346e411930c4\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
261            android:name="com.google.android.gms.version"
261-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92e8c801482673d28198346e411930c4\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
262            android:value="@integer/google_play_services_version" />
262-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92e8c801482673d28198346e411930c4\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
263
264        <receiver
264-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
265            android:name="androidx.profileinstaller.ProfileInstallReceiver"
265-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
266            android:directBootAware="false"
266-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
267            android:enabled="true"
267-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
268            android:exported="true"
268-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
269            android:permission="android.permission.DUMP" >
269-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
270            <intent-filter>
270-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
271                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
271-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
271-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
272            </intent-filter>
273            <intent-filter>
273-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
274                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
274-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
274-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
275            </intent-filter>
276            <intent-filter>
276-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
277                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
277-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
277-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
278            </intent-filter>
279            <intent-filter>
279-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
280                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
280-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
280-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
281            </intent-filter>
282        </receiver>
283    </application>
284
285</manifest>
