// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemLegendBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final CheckBox checkBox;

  @NonNull
  public final View colorIndicator;

  @NonNull
  public final TextView scoreName;

  private ItemLegendBinding(@NonNull LinearLayout rootView, @NonNull CheckBox checkBox,
      @NonNull View colorIndicator, @NonNull TextView scoreName) {
    this.rootView = rootView;
    this.checkBox = checkBox;
    this.colorIndicator = colorIndicator;
    this.scoreName = scoreName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemLegendBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemLegendBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_legend, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemLegendBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.checkBox;
      CheckBox checkBox = ViewBindings.findChildViewById(rootView, id);
      if (checkBox == null) {
        break missingId;
      }

      id = R.id.colorIndicator;
      View colorIndicator = ViewBindings.findChildViewById(rootView, id);
      if (colorIndicator == null) {
        break missingId;
      }

      id = R.id.scoreName;
      TextView scoreName = ViewBindings.findChildViewById(rootView, id);
      if (scoreName == null) {
        break missingId;
      }

      return new ItemLegendBinding((LinearLayout) rootView, checkBox, colorIndicator, scoreName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
