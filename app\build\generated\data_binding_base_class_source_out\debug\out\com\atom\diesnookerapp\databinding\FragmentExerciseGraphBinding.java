// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentExerciseGraphBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView categoryText;

  @NonNull
  public final FrameLayout graphContainer;

  @NonNull
  public final MaterialButton timeframeButton;

  private FragmentExerciseGraphBinding(@NonNull LinearLayout rootView,
      @NonNull TextView categoryText, @NonNull FrameLayout graphContainer,
      @NonNull MaterialButton timeframeButton) {
    this.rootView = rootView;
    this.categoryText = categoryText;
    this.graphContainer = graphContainer;
    this.timeframeButton = timeframeButton;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentExerciseGraphBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentExerciseGraphBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_exercise_graph, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentExerciseGraphBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.categoryText;
      TextView categoryText = ViewBindings.findChildViewById(rootView, id);
      if (categoryText == null) {
        break missingId;
      }

      id = R.id.graphContainer;
      FrameLayout graphContainer = ViewBindings.findChildViewById(rootView, id);
      if (graphContainer == null) {
        break missingId;
      }

      id = R.id.timeframeButton;
      MaterialButton timeframeButton = ViewBindings.findChildViewById(rootView, id);
      if (timeframeButton == null) {
        break missingId;
      }

      return new FragmentExerciseGraphBinding((LinearLayout) rootView, categoryText, graphContainer,
          timeframeButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
