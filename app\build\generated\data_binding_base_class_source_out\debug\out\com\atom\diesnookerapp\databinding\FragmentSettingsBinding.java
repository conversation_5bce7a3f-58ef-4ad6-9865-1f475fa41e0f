// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentSettingsBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialCardView accountCard;

  @NonNull
  public final ThemePreviewBlueBinding blueThemePreviewInclude;

  @NonNull
  public final RadioButton blueThemeRadioButton;

  @NonNull
  public final ThemePreviewCrimsonBinding crimsonThemePreviewInclude;

  @NonNull
  public final RadioButton crimsonThemeRadioButton;

  @NonNull
  public final ThemePreviewDarkBlueBinding darkBlueThemePreviewInclude;

  @NonNull
  public final RadioButton darkBlueThemeRadioButton;

  @NonNull
  public final ThemePreviewDarkBinding darkThemePreviewInclude;

  @NonNull
  public final RadioButton darkThemeRadioButton;

  @NonNull
  public final TextView emailTextView;

  @NonNull
  public final TextView lastSyncTextView;

  @NonNull
  public final ThemePreviewLightBinding lightThemePreviewInclude;

  @NonNull
  public final RadioButton lightThemeRadioButton;

  @NonNull
  public final Button loginButton;

  @NonNull
  public final Button logoutButton;

  @NonNull
  public final ThemePreviewNeonBinding neonThemePreviewInclude;

  @NonNull
  public final RadioButton neonThemeRadioButton;

  @NonNull
  public final ThemePreviewOceanBinding oceanThemePreviewInclude;

  @NonNull
  public final RadioButton oceanThemeRadioButton;

  @NonNull
  public final Button openWebButton;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final ThemePreviewSnookerBinding snookerThemePreviewInclude;

  @NonNull
  public final RadioButton snookerThemeRadioButton;

  @NonNull
  public final MaterialCardView syncCard;

  @NonNull
  public final Button syncNowButton;

  @NonNull
  public final TextView syncStatusTextView;

  @NonNull
  public final RadioButton systemThemeRadioButton;

  @NonNull
  public final MaterialCardView themeCard;

  @NonNull
  public final RadioGroup themeRadioGroup;

  @NonNull
  public final TextView titleTextView;

  @NonNull
  public final Button userManagementButton;

  @NonNull
  public final MaterialCardView userManagementCard;

  @NonNull
  public final MaterialCardView webAccessCard;

  private FragmentSettingsBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialCardView accountCard,
      @NonNull ThemePreviewBlueBinding blueThemePreviewInclude,
      @NonNull RadioButton blueThemeRadioButton,
      @NonNull ThemePreviewCrimsonBinding crimsonThemePreviewInclude,
      @NonNull RadioButton crimsonThemeRadioButton,
      @NonNull ThemePreviewDarkBlueBinding darkBlueThemePreviewInclude,
      @NonNull RadioButton darkBlueThemeRadioButton,
      @NonNull ThemePreviewDarkBinding darkThemePreviewInclude,
      @NonNull RadioButton darkThemeRadioButton, @NonNull TextView emailTextView,
      @NonNull TextView lastSyncTextView,
      @NonNull ThemePreviewLightBinding lightThemePreviewInclude,
      @NonNull RadioButton lightThemeRadioButton, @NonNull Button loginButton,
      @NonNull Button logoutButton, @NonNull ThemePreviewNeonBinding neonThemePreviewInclude,
      @NonNull RadioButton neonThemeRadioButton,
      @NonNull ThemePreviewOceanBinding oceanThemePreviewInclude,
      @NonNull RadioButton oceanThemeRadioButton, @NonNull Button openWebButton,
      @NonNull ProgressBar progressBar,
      @NonNull ThemePreviewSnookerBinding snookerThemePreviewInclude,
      @NonNull RadioButton snookerThemeRadioButton, @NonNull MaterialCardView syncCard,
      @NonNull Button syncNowButton, @NonNull TextView syncStatusTextView,
      @NonNull RadioButton systemThemeRadioButton, @NonNull MaterialCardView themeCard,
      @NonNull RadioGroup themeRadioGroup, @NonNull TextView titleTextView,
      @NonNull Button userManagementButton, @NonNull MaterialCardView userManagementCard,
      @NonNull MaterialCardView webAccessCard) {
    this.rootView = rootView;
    this.accountCard = accountCard;
    this.blueThemePreviewInclude = blueThemePreviewInclude;
    this.blueThemeRadioButton = blueThemeRadioButton;
    this.crimsonThemePreviewInclude = crimsonThemePreviewInclude;
    this.crimsonThemeRadioButton = crimsonThemeRadioButton;
    this.darkBlueThemePreviewInclude = darkBlueThemePreviewInclude;
    this.darkBlueThemeRadioButton = darkBlueThemeRadioButton;
    this.darkThemePreviewInclude = darkThemePreviewInclude;
    this.darkThemeRadioButton = darkThemeRadioButton;
    this.emailTextView = emailTextView;
    this.lastSyncTextView = lastSyncTextView;
    this.lightThemePreviewInclude = lightThemePreviewInclude;
    this.lightThemeRadioButton = lightThemeRadioButton;
    this.loginButton = loginButton;
    this.logoutButton = logoutButton;
    this.neonThemePreviewInclude = neonThemePreviewInclude;
    this.neonThemeRadioButton = neonThemeRadioButton;
    this.oceanThemePreviewInclude = oceanThemePreviewInclude;
    this.oceanThemeRadioButton = oceanThemeRadioButton;
    this.openWebButton = openWebButton;
    this.progressBar = progressBar;
    this.snookerThemePreviewInclude = snookerThemePreviewInclude;
    this.snookerThemeRadioButton = snookerThemeRadioButton;
    this.syncCard = syncCard;
    this.syncNowButton = syncNowButton;
    this.syncStatusTextView = syncStatusTextView;
    this.systemThemeRadioButton = systemThemeRadioButton;
    this.themeCard = themeCard;
    this.themeRadioGroup = themeRadioGroup;
    this.titleTextView = titleTextView;
    this.userManagementButton = userManagementButton;
    this.userManagementCard = userManagementCard;
    this.webAccessCard = webAccessCard;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.accountCard;
      MaterialCardView accountCard = ViewBindings.findChildViewById(rootView, id);
      if (accountCard == null) {
        break missingId;
      }

      id = R.id.blueThemePreviewInclude;
      View blueThemePreviewInclude = ViewBindings.findChildViewById(rootView, id);
      if (blueThemePreviewInclude == null) {
        break missingId;
      }
      ThemePreviewBlueBinding binding_blueThemePreviewInclude = ThemePreviewBlueBinding.bind(blueThemePreviewInclude);

      id = R.id.blueThemeRadioButton;
      RadioButton blueThemeRadioButton = ViewBindings.findChildViewById(rootView, id);
      if (blueThemeRadioButton == null) {
        break missingId;
      }

      id = R.id.crimsonThemePreviewInclude;
      View crimsonThemePreviewInclude = ViewBindings.findChildViewById(rootView, id);
      if (crimsonThemePreviewInclude == null) {
        break missingId;
      }
      ThemePreviewCrimsonBinding binding_crimsonThemePreviewInclude = ThemePreviewCrimsonBinding.bind(crimsonThemePreviewInclude);

      id = R.id.crimsonThemeRadioButton;
      RadioButton crimsonThemeRadioButton = ViewBindings.findChildViewById(rootView, id);
      if (crimsonThemeRadioButton == null) {
        break missingId;
      }

      id = R.id.darkBlueThemePreviewInclude;
      View darkBlueThemePreviewInclude = ViewBindings.findChildViewById(rootView, id);
      if (darkBlueThemePreviewInclude == null) {
        break missingId;
      }
      ThemePreviewDarkBlueBinding binding_darkBlueThemePreviewInclude = ThemePreviewDarkBlueBinding.bind(darkBlueThemePreviewInclude);

      id = R.id.darkBlueThemeRadioButton;
      RadioButton darkBlueThemeRadioButton = ViewBindings.findChildViewById(rootView, id);
      if (darkBlueThemeRadioButton == null) {
        break missingId;
      }

      id = R.id.darkThemePreviewInclude;
      View darkThemePreviewInclude = ViewBindings.findChildViewById(rootView, id);
      if (darkThemePreviewInclude == null) {
        break missingId;
      }
      ThemePreviewDarkBinding binding_darkThemePreviewInclude = ThemePreviewDarkBinding.bind(darkThemePreviewInclude);

      id = R.id.darkThemeRadioButton;
      RadioButton darkThemeRadioButton = ViewBindings.findChildViewById(rootView, id);
      if (darkThemeRadioButton == null) {
        break missingId;
      }

      id = R.id.emailTextView;
      TextView emailTextView = ViewBindings.findChildViewById(rootView, id);
      if (emailTextView == null) {
        break missingId;
      }

      id = R.id.lastSyncTextView;
      TextView lastSyncTextView = ViewBindings.findChildViewById(rootView, id);
      if (lastSyncTextView == null) {
        break missingId;
      }

      id = R.id.lightThemePreviewInclude;
      View lightThemePreviewInclude = ViewBindings.findChildViewById(rootView, id);
      if (lightThemePreviewInclude == null) {
        break missingId;
      }
      ThemePreviewLightBinding binding_lightThemePreviewInclude = ThemePreviewLightBinding.bind(lightThemePreviewInclude);

      id = R.id.lightThemeRadioButton;
      RadioButton lightThemeRadioButton = ViewBindings.findChildViewById(rootView, id);
      if (lightThemeRadioButton == null) {
        break missingId;
      }

      id = R.id.loginButton;
      Button loginButton = ViewBindings.findChildViewById(rootView, id);
      if (loginButton == null) {
        break missingId;
      }

      id = R.id.logoutButton;
      Button logoutButton = ViewBindings.findChildViewById(rootView, id);
      if (logoutButton == null) {
        break missingId;
      }

      id = R.id.neonThemePreviewInclude;
      View neonThemePreviewInclude = ViewBindings.findChildViewById(rootView, id);
      if (neonThemePreviewInclude == null) {
        break missingId;
      }
      ThemePreviewNeonBinding binding_neonThemePreviewInclude = ThemePreviewNeonBinding.bind(neonThemePreviewInclude);

      id = R.id.neonThemeRadioButton;
      RadioButton neonThemeRadioButton = ViewBindings.findChildViewById(rootView, id);
      if (neonThemeRadioButton == null) {
        break missingId;
      }

      id = R.id.oceanThemePreviewInclude;
      View oceanThemePreviewInclude = ViewBindings.findChildViewById(rootView, id);
      if (oceanThemePreviewInclude == null) {
        break missingId;
      }
      ThemePreviewOceanBinding binding_oceanThemePreviewInclude = ThemePreviewOceanBinding.bind(oceanThemePreviewInclude);

      id = R.id.oceanThemeRadioButton;
      RadioButton oceanThemeRadioButton = ViewBindings.findChildViewById(rootView, id);
      if (oceanThemeRadioButton == null) {
        break missingId;
      }

      id = R.id.openWebButton;
      Button openWebButton = ViewBindings.findChildViewById(rootView, id);
      if (openWebButton == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.snookerThemePreviewInclude;
      View snookerThemePreviewInclude = ViewBindings.findChildViewById(rootView, id);
      if (snookerThemePreviewInclude == null) {
        break missingId;
      }
      ThemePreviewSnookerBinding binding_snookerThemePreviewInclude = ThemePreviewSnookerBinding.bind(snookerThemePreviewInclude);

      id = R.id.snookerThemeRadioButton;
      RadioButton snookerThemeRadioButton = ViewBindings.findChildViewById(rootView, id);
      if (snookerThemeRadioButton == null) {
        break missingId;
      }

      id = R.id.syncCard;
      MaterialCardView syncCard = ViewBindings.findChildViewById(rootView, id);
      if (syncCard == null) {
        break missingId;
      }

      id = R.id.syncNowButton;
      Button syncNowButton = ViewBindings.findChildViewById(rootView, id);
      if (syncNowButton == null) {
        break missingId;
      }

      id = R.id.syncStatusTextView;
      TextView syncStatusTextView = ViewBindings.findChildViewById(rootView, id);
      if (syncStatusTextView == null) {
        break missingId;
      }

      id = R.id.systemThemeRadioButton;
      RadioButton systemThemeRadioButton = ViewBindings.findChildViewById(rootView, id);
      if (systemThemeRadioButton == null) {
        break missingId;
      }

      id = R.id.themeCard;
      MaterialCardView themeCard = ViewBindings.findChildViewById(rootView, id);
      if (themeCard == null) {
        break missingId;
      }

      id = R.id.themeRadioGroup;
      RadioGroup themeRadioGroup = ViewBindings.findChildViewById(rootView, id);
      if (themeRadioGroup == null) {
        break missingId;
      }

      id = R.id.titleTextView;
      TextView titleTextView = ViewBindings.findChildViewById(rootView, id);
      if (titleTextView == null) {
        break missingId;
      }

      id = R.id.userManagementButton;
      Button userManagementButton = ViewBindings.findChildViewById(rootView, id);
      if (userManagementButton == null) {
        break missingId;
      }

      id = R.id.userManagementCard;
      MaterialCardView userManagementCard = ViewBindings.findChildViewById(rootView, id);
      if (userManagementCard == null) {
        break missingId;
      }

      id = R.id.webAccessCard;
      MaterialCardView webAccessCard = ViewBindings.findChildViewById(rootView, id);
      if (webAccessCard == null) {
        break missingId;
      }

      return new FragmentSettingsBinding((ConstraintLayout) rootView, accountCard,
          binding_blueThemePreviewInclude, blueThemeRadioButton, binding_crimsonThemePreviewInclude,
          crimsonThemeRadioButton, binding_darkBlueThemePreviewInclude, darkBlueThemeRadioButton,
          binding_darkThemePreviewInclude, darkThemeRadioButton, emailTextView, lastSyncTextView,
          binding_lightThemePreviewInclude, lightThemeRadioButton, loginButton, logoutButton,
          binding_neonThemePreviewInclude, neonThemeRadioButton, binding_oceanThemePreviewInclude,
          oceanThemeRadioButton, openWebButton, progressBar, binding_snookerThemePreviewInclude,
          snookerThemeRadioButton, syncCard, syncNowButton, syncStatusTextView,
          systemThemeRadioButton, themeCard, themeRadioGroup, titleTextView, userManagementButton,
          userManagementCard, webAccessCard);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
