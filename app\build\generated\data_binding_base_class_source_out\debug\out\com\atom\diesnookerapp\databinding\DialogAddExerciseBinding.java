// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.NumberPicker;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.atom.diesnookerapp.ui.utils.SnookerTabLayout;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAddExerciseBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton addButton;

  @NonNull
  public final MaterialButton cancelButton;

  @NonNull
  public final SnookerTabLayout categoryTabs;

  @NonNull
  public final FrameLayout contentContainer;

  @NonNull
  public final ConstraintLayout customExerciseLayout;

  @NonNull
  public final TextView dialogTitle;

  @NonNull
  public final TextInputEditText exerciseNameInput;

  @NonNull
  public final TextInputLayout exerciseNameLayout;

  @NonNull
  public final AutoCompleteTextView exerciseTypeDropdown;

  @NonNull
  public final TextInputLayout exerciseTypeLayout;

  @NonNull
  public final RecyclerView exercisesRecyclerView;

  @NonNull
  public final LinearLayout targetCountLayout;

  @NonNull
  public final NumberPicker targetCountPicker;

  private DialogAddExerciseBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton addButton, @NonNull MaterialButton cancelButton,
      @NonNull SnookerTabLayout categoryTabs, @NonNull FrameLayout contentContainer,
      @NonNull ConstraintLayout customExerciseLayout, @NonNull TextView dialogTitle,
      @NonNull TextInputEditText exerciseNameInput, @NonNull TextInputLayout exerciseNameLayout,
      @NonNull AutoCompleteTextView exerciseTypeDropdown,
      @NonNull TextInputLayout exerciseTypeLayout, @NonNull RecyclerView exercisesRecyclerView,
      @NonNull LinearLayout targetCountLayout, @NonNull NumberPicker targetCountPicker) {
    this.rootView = rootView;
    this.addButton = addButton;
    this.cancelButton = cancelButton;
    this.categoryTabs = categoryTabs;
    this.contentContainer = contentContainer;
    this.customExerciseLayout = customExerciseLayout;
    this.dialogTitle = dialogTitle;
    this.exerciseNameInput = exerciseNameInput;
    this.exerciseNameLayout = exerciseNameLayout;
    this.exerciseTypeDropdown = exerciseTypeDropdown;
    this.exerciseTypeLayout = exerciseTypeLayout;
    this.exercisesRecyclerView = exercisesRecyclerView;
    this.targetCountLayout = targetCountLayout;
    this.targetCountPicker = targetCountPicker;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAddExerciseBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAddExerciseBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_add_exercise, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAddExerciseBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addButton;
      MaterialButton addButton = ViewBindings.findChildViewById(rootView, id);
      if (addButton == null) {
        break missingId;
      }

      id = R.id.cancelButton;
      MaterialButton cancelButton = ViewBindings.findChildViewById(rootView, id);
      if (cancelButton == null) {
        break missingId;
      }

      id = R.id.categoryTabs;
      SnookerTabLayout categoryTabs = ViewBindings.findChildViewById(rootView, id);
      if (categoryTabs == null) {
        break missingId;
      }

      id = R.id.contentContainer;
      FrameLayout contentContainer = ViewBindings.findChildViewById(rootView, id);
      if (contentContainer == null) {
        break missingId;
      }

      id = R.id.customExerciseLayout;
      ConstraintLayout customExerciseLayout = ViewBindings.findChildViewById(rootView, id);
      if (customExerciseLayout == null) {
        break missingId;
      }

      id = R.id.dialogTitle;
      TextView dialogTitle = ViewBindings.findChildViewById(rootView, id);
      if (dialogTitle == null) {
        break missingId;
      }

      id = R.id.exerciseNameInput;
      TextInputEditText exerciseNameInput = ViewBindings.findChildViewById(rootView, id);
      if (exerciseNameInput == null) {
        break missingId;
      }

      id = R.id.exerciseNameLayout;
      TextInputLayout exerciseNameLayout = ViewBindings.findChildViewById(rootView, id);
      if (exerciseNameLayout == null) {
        break missingId;
      }

      id = R.id.exerciseTypeDropdown;
      AutoCompleteTextView exerciseTypeDropdown = ViewBindings.findChildViewById(rootView, id);
      if (exerciseTypeDropdown == null) {
        break missingId;
      }

      id = R.id.exerciseTypeLayout;
      TextInputLayout exerciseTypeLayout = ViewBindings.findChildViewById(rootView, id);
      if (exerciseTypeLayout == null) {
        break missingId;
      }

      id = R.id.exercisesRecyclerView;
      RecyclerView exercisesRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (exercisesRecyclerView == null) {
        break missingId;
      }

      id = R.id.targetCountLayout;
      LinearLayout targetCountLayout = ViewBindings.findChildViewById(rootView, id);
      if (targetCountLayout == null) {
        break missingId;
      }

      id = R.id.targetCountPicker;
      NumberPicker targetCountPicker = ViewBindings.findChildViewById(rootView, id);
      if (targetCountPicker == null) {
        break missingId;
      }

      return new DialogAddExerciseBinding((ConstraintLayout) rootView, addButton, cancelButton,
          categoryTabs, contentContainer, customExerciseLayout, dialogTitle, exerciseNameInput,
          exerciseNameLayout, exerciseTypeDropdown, exerciseTypeLayout, exercisesRecyclerView,
          targetCountLayout, targetCountPicker);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
