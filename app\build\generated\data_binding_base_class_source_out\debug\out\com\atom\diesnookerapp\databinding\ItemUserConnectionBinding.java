// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.chip.Chip;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemUserConnectionBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final MaterialButton editButton;

  @NonNull
  public final TextView emailTextView;

  @NonNull
  public final TextView nameTextView;

  @NonNull
  public final TextView permissionsLabel;

  @NonNull
  public final TextView permissionsTextView;

  @NonNull
  public final MaterialButton removeButton;

  @NonNull
  public final TextView roleTextView;

  @NonNull
  public final Chip statusChip;

  private ItemUserConnectionBinding(@NonNull MaterialCardView rootView,
      @NonNull MaterialButton editButton, @NonNull TextView emailTextView,
      @NonNull TextView nameTextView, @NonNull TextView permissionsLabel,
      @NonNull TextView permissionsTextView, @NonNull MaterialButton removeButton,
      @NonNull TextView roleTextView, @NonNull Chip statusChip) {
    this.rootView = rootView;
    this.editButton = editButton;
    this.emailTextView = emailTextView;
    this.nameTextView = nameTextView;
    this.permissionsLabel = permissionsLabel;
    this.permissionsTextView = permissionsTextView;
    this.removeButton = removeButton;
    this.roleTextView = roleTextView;
    this.statusChip = statusChip;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemUserConnectionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemUserConnectionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_user_connection, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemUserConnectionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.editButton;
      MaterialButton editButton = ViewBindings.findChildViewById(rootView, id);
      if (editButton == null) {
        break missingId;
      }

      id = R.id.emailTextView;
      TextView emailTextView = ViewBindings.findChildViewById(rootView, id);
      if (emailTextView == null) {
        break missingId;
      }

      id = R.id.nameTextView;
      TextView nameTextView = ViewBindings.findChildViewById(rootView, id);
      if (nameTextView == null) {
        break missingId;
      }

      id = R.id.permissionsLabel;
      TextView permissionsLabel = ViewBindings.findChildViewById(rootView, id);
      if (permissionsLabel == null) {
        break missingId;
      }

      id = R.id.permissionsTextView;
      TextView permissionsTextView = ViewBindings.findChildViewById(rootView, id);
      if (permissionsTextView == null) {
        break missingId;
      }

      id = R.id.removeButton;
      MaterialButton removeButton = ViewBindings.findChildViewById(rootView, id);
      if (removeButton == null) {
        break missingId;
      }

      id = R.id.roleTextView;
      TextView roleTextView = ViewBindings.findChildViewById(rootView, id);
      if (roleTextView == null) {
        break missingId;
      }

      id = R.id.statusChip;
      Chip statusChip = ViewBindings.findChildViewById(rootView, id);
      if (statusChip == null) {
        break missingId;
      }

      return new ItemUserConnectionBinding((MaterialCardView) rootView, editButton, emailTextView,
          nameTextView, permissionsLabel, permissionsTextView, removeButton, roleTextView,
          statusChip);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
