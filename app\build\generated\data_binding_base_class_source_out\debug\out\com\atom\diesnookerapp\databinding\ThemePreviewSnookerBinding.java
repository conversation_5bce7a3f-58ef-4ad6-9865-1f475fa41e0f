// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import com.atom.diesnookerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;

public final class ThemePreviewSnookerBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final CardView snookerThemePreview;

  private ThemePreviewSnookerBinding(@NonNull CardView rootView,
      @NonNull CardView snookerThemePreview) {
    this.rootView = rootView;
    this.snookerThemePreview = snookerThemePreview;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ThemePreviewSnookerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ThemePreviewSnookerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.theme_preview_snooker, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ThemePreviewSnookerBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    CardView snookerThemePreview = (CardView) rootView;

    return new ThemePreviewSnookerBinding((CardView) rootView, snookerThemePreview);
  }
}
