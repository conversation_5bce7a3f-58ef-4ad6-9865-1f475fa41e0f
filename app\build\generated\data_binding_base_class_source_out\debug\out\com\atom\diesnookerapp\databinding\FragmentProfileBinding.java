// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentProfileBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView emailLabel;

  @NonNull
  public final TextView emailTextView;

  @NonNull
  public final TextView lastSyncLabel;

  @NonNull
  public final TextView lastSyncTextView;

  @NonNull
  public final Button logoutButton;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final Button syncNowButton;

  @NonNull
  public final TextView syncResultLabel;

  @NonNull
  public final TextView syncResultTextView;

  @NonNull
  public final TextView syncStatusLabel;

  @NonNull
  public final TextView syncStatusTextView;

  @NonNull
  public final TextView titleTextView;

  private FragmentProfileBinding(@NonNull ConstraintLayout rootView, @NonNull TextView emailLabel,
      @NonNull TextView emailTextView, @NonNull TextView lastSyncLabel,
      @NonNull TextView lastSyncTextView, @NonNull Button logoutButton,
      @NonNull ProgressBar progressBar, @NonNull Button syncNowButton,
      @NonNull TextView syncResultLabel, @NonNull TextView syncResultTextView,
      @NonNull TextView syncStatusLabel, @NonNull TextView syncStatusTextView,
      @NonNull TextView titleTextView) {
    this.rootView = rootView;
    this.emailLabel = emailLabel;
    this.emailTextView = emailTextView;
    this.lastSyncLabel = lastSyncLabel;
    this.lastSyncTextView = lastSyncTextView;
    this.logoutButton = logoutButton;
    this.progressBar = progressBar;
    this.syncNowButton = syncNowButton;
    this.syncResultLabel = syncResultLabel;
    this.syncResultTextView = syncResultTextView;
    this.syncStatusLabel = syncStatusLabel;
    this.syncStatusTextView = syncStatusTextView;
    this.titleTextView = titleTextView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentProfileBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentProfileBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_profile, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentProfileBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.emailLabel;
      TextView emailLabel = ViewBindings.findChildViewById(rootView, id);
      if (emailLabel == null) {
        break missingId;
      }

      id = R.id.emailTextView;
      TextView emailTextView = ViewBindings.findChildViewById(rootView, id);
      if (emailTextView == null) {
        break missingId;
      }

      id = R.id.lastSyncLabel;
      TextView lastSyncLabel = ViewBindings.findChildViewById(rootView, id);
      if (lastSyncLabel == null) {
        break missingId;
      }

      id = R.id.lastSyncTextView;
      TextView lastSyncTextView = ViewBindings.findChildViewById(rootView, id);
      if (lastSyncTextView == null) {
        break missingId;
      }

      id = R.id.logoutButton;
      Button logoutButton = ViewBindings.findChildViewById(rootView, id);
      if (logoutButton == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.syncNowButton;
      Button syncNowButton = ViewBindings.findChildViewById(rootView, id);
      if (syncNowButton == null) {
        break missingId;
      }

      id = R.id.syncResultLabel;
      TextView syncResultLabel = ViewBindings.findChildViewById(rootView, id);
      if (syncResultLabel == null) {
        break missingId;
      }

      id = R.id.syncResultTextView;
      TextView syncResultTextView = ViewBindings.findChildViewById(rootView, id);
      if (syncResultTextView == null) {
        break missingId;
      }

      id = R.id.syncStatusLabel;
      TextView syncStatusLabel = ViewBindings.findChildViewById(rootView, id);
      if (syncStatusLabel == null) {
        break missingId;
      }

      id = R.id.syncStatusTextView;
      TextView syncStatusTextView = ViewBindings.findChildViewById(rootView, id);
      if (syncStatusTextView == null) {
        break missingId;
      }

      id = R.id.titleTextView;
      TextView titleTextView = ViewBindings.findChildViewById(rootView, id);
      if (titleTextView == null) {
        break missingId;
      }

      return new FragmentProfileBinding((ConstraintLayout) rootView, emailLabel, emailTextView,
          lastSyncLabel, lastSyncTextView, logoutButton, progressBar, syncNowButton,
          syncResultLabel, syncResultTextView, syncStatusLabel, syncStatusTextView, titleTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
