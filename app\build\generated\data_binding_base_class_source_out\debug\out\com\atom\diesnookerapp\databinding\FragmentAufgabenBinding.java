// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentAufgabenBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ExtendedFloatingActionButton historyButton;

  @NonNull
  public final FloatingActionButton manageTasksButton;

  @NonNull
  public final RecyclerView taskCategoriesRecyclerView;

  @NonNull
  public final TextView titleText;

  private FragmentAufgabenBinding(@NonNull ConstraintLayout rootView,
      @NonNull ExtendedFloatingActionButton historyButton,
      @NonNull FloatingActionButton manageTasksButton,
      @NonNull RecyclerView taskCategoriesRecyclerView, @NonNull TextView titleText) {
    this.rootView = rootView;
    this.historyButton = historyButton;
    this.manageTasksButton = manageTasksButton;
    this.taskCategoriesRecyclerView = taskCategoriesRecyclerView;
    this.titleText = titleText;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentAufgabenBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentAufgabenBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_aufgaben, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentAufgabenBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.historyButton;
      ExtendedFloatingActionButton historyButton = ViewBindings.findChildViewById(rootView, id);
      if (historyButton == null) {
        break missingId;
      }

      id = R.id.manageTasksButton;
      FloatingActionButton manageTasksButton = ViewBindings.findChildViewById(rootView, id);
      if (manageTasksButton == null) {
        break missingId;
      }

      id = R.id.taskCategoriesRecyclerView;
      RecyclerView taskCategoriesRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (taskCategoriesRecyclerView == null) {
        break missingId;
      }

      id = R.id.titleText;
      TextView titleText = ViewBindings.findChildViewById(rootView, id);
      if (titleText == null) {
        break missingId;
      }

      return new FragmentAufgabenBinding((ConstraintLayout) rootView, historyButton,
          manageTasksButton, taskCategoriesRecyclerView, titleText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
