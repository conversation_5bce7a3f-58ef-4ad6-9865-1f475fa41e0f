// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.MaterialAutoCompleteTextView;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAddEditExerciseBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton cancelButton;

  @NonNull
  public final MaterialAutoCompleteTextView exerciseCategoryAutoCompleteTextView;

  @NonNull
  public final TextInputLayout exerciseCategoryInputLayout;

  @NonNull
  public final TextInputEditText exerciseDescriptionEditText;

  @NonNull
  public final TextInputLayout exerciseDescriptionInputLayout;

  @NonNull
  public final TextInputEditText exerciseNameEditText;

  @NonNull
  public final TextInputLayout exerciseNameInputLayout;

  @NonNull
  public final MaterialAutoCompleteTextView exerciseTypeAutoCompleteTextView;

  @NonNull
  public final TextInputLayout exerciseTypeInputLayout;

  @NonNull
  public final TextInputEditText newCategoryEditText;

  @NonNull
  public final TextInputLayout newCategoryInputLayout;

  @NonNull
  public final MaterialButton saveButton;

  private DialogAddEditExerciseBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton cancelButton,
      @NonNull MaterialAutoCompleteTextView exerciseCategoryAutoCompleteTextView,
      @NonNull TextInputLayout exerciseCategoryInputLayout,
      @NonNull TextInputEditText exerciseDescriptionEditText,
      @NonNull TextInputLayout exerciseDescriptionInputLayout,
      @NonNull TextInputEditText exerciseNameEditText,
      @NonNull TextInputLayout exerciseNameInputLayout,
      @NonNull MaterialAutoCompleteTextView exerciseTypeAutoCompleteTextView,
      @NonNull TextInputLayout exerciseTypeInputLayout,
      @NonNull TextInputEditText newCategoryEditText,
      @NonNull TextInputLayout newCategoryInputLayout, @NonNull MaterialButton saveButton) {
    this.rootView = rootView;
    this.cancelButton = cancelButton;
    this.exerciseCategoryAutoCompleteTextView = exerciseCategoryAutoCompleteTextView;
    this.exerciseCategoryInputLayout = exerciseCategoryInputLayout;
    this.exerciseDescriptionEditText = exerciseDescriptionEditText;
    this.exerciseDescriptionInputLayout = exerciseDescriptionInputLayout;
    this.exerciseNameEditText = exerciseNameEditText;
    this.exerciseNameInputLayout = exerciseNameInputLayout;
    this.exerciseTypeAutoCompleteTextView = exerciseTypeAutoCompleteTextView;
    this.exerciseTypeInputLayout = exerciseTypeInputLayout;
    this.newCategoryEditText = newCategoryEditText;
    this.newCategoryInputLayout = newCategoryInputLayout;
    this.saveButton = saveButton;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAddEditExerciseBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAddEditExerciseBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_add_edit_exercise, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAddEditExerciseBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cancelButton;
      MaterialButton cancelButton = ViewBindings.findChildViewById(rootView, id);
      if (cancelButton == null) {
        break missingId;
      }

      id = R.id.exerciseCategoryAutoCompleteTextView;
      MaterialAutoCompleteTextView exerciseCategoryAutoCompleteTextView = ViewBindings.findChildViewById(rootView, id);
      if (exerciseCategoryAutoCompleteTextView == null) {
        break missingId;
      }

      id = R.id.exerciseCategoryInputLayout;
      TextInputLayout exerciseCategoryInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (exerciseCategoryInputLayout == null) {
        break missingId;
      }

      id = R.id.exerciseDescriptionEditText;
      TextInputEditText exerciseDescriptionEditText = ViewBindings.findChildViewById(rootView, id);
      if (exerciseDescriptionEditText == null) {
        break missingId;
      }

      id = R.id.exerciseDescriptionInputLayout;
      TextInputLayout exerciseDescriptionInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (exerciseDescriptionInputLayout == null) {
        break missingId;
      }

      id = R.id.exerciseNameEditText;
      TextInputEditText exerciseNameEditText = ViewBindings.findChildViewById(rootView, id);
      if (exerciseNameEditText == null) {
        break missingId;
      }

      id = R.id.exerciseNameInputLayout;
      TextInputLayout exerciseNameInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (exerciseNameInputLayout == null) {
        break missingId;
      }

      id = R.id.exerciseTypeAutoCompleteTextView;
      MaterialAutoCompleteTextView exerciseTypeAutoCompleteTextView = ViewBindings.findChildViewById(rootView, id);
      if (exerciseTypeAutoCompleteTextView == null) {
        break missingId;
      }

      id = R.id.exerciseTypeInputLayout;
      TextInputLayout exerciseTypeInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (exerciseTypeInputLayout == null) {
        break missingId;
      }

      id = R.id.newCategoryEditText;
      TextInputEditText newCategoryEditText = ViewBindings.findChildViewById(rootView, id);
      if (newCategoryEditText == null) {
        break missingId;
      }

      id = R.id.newCategoryInputLayout;
      TextInputLayout newCategoryInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (newCategoryInputLayout == null) {
        break missingId;
      }

      id = R.id.saveButton;
      MaterialButton saveButton = ViewBindings.findChildViewById(rootView, id);
      if (saveButton == null) {
        break missingId;
      }

      return new DialogAddEditExerciseBinding((LinearLayout) rootView, cancelButton,
          exerciseCategoryAutoCompleteTextView, exerciseCategoryInputLayout,
          exerciseDescriptionEditText, exerciseDescriptionInputLayout, exerciseNameEditText,
          exerciseNameInputLayout, exerciseTypeAutoCompleteTextView, exerciseTypeInputLayout,
          newCategoryEditText, newCategoryInputLayout, saveButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
