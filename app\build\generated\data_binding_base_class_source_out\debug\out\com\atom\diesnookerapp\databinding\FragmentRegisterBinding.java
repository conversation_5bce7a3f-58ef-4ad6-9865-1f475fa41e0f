// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentRegisterBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextInputEditText confirmPasswordEditText;

  @NonNull
  public final TextInputLayout confirmPasswordInputLayout;

  @NonNull
  public final TextInputEditText emailEditText;

  @NonNull
  public final TextInputLayout emailInputLayout;

  @NonNull
  public final Button loginButton;

  @NonNull
  public final TextInputEditText nameEditText;

  @NonNull
  public final TextInputLayout nameInputLayout;

  @NonNull
  public final TextInputEditText passwordEditText;

  @NonNull
  public final TextInputLayout passwordInputLayout;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final Button registerButton;

  @NonNull
  public final AutoCompleteTextView roleDropdown;

  @NonNull
  public final TextInputLayout roleInputLayout;

  @NonNull
  public final TextView titleTextView;

  private FragmentRegisterBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextInputEditText confirmPasswordEditText,
      @NonNull TextInputLayout confirmPasswordInputLayout, @NonNull TextInputEditText emailEditText,
      @NonNull TextInputLayout emailInputLayout, @NonNull Button loginButton,
      @NonNull TextInputEditText nameEditText, @NonNull TextInputLayout nameInputLayout,
      @NonNull TextInputEditText passwordEditText, @NonNull TextInputLayout passwordInputLayout,
      @NonNull ProgressBar progressBar, @NonNull Button registerButton,
      @NonNull AutoCompleteTextView roleDropdown, @NonNull TextInputLayout roleInputLayout,
      @NonNull TextView titleTextView) {
    this.rootView = rootView;
    this.confirmPasswordEditText = confirmPasswordEditText;
    this.confirmPasswordInputLayout = confirmPasswordInputLayout;
    this.emailEditText = emailEditText;
    this.emailInputLayout = emailInputLayout;
    this.loginButton = loginButton;
    this.nameEditText = nameEditText;
    this.nameInputLayout = nameInputLayout;
    this.passwordEditText = passwordEditText;
    this.passwordInputLayout = passwordInputLayout;
    this.progressBar = progressBar;
    this.registerButton = registerButton;
    this.roleDropdown = roleDropdown;
    this.roleInputLayout = roleInputLayout;
    this.titleTextView = titleTextView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentRegisterBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentRegisterBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_register, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentRegisterBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.confirmPasswordEditText;
      TextInputEditText confirmPasswordEditText = ViewBindings.findChildViewById(rootView, id);
      if (confirmPasswordEditText == null) {
        break missingId;
      }

      id = R.id.confirmPasswordInputLayout;
      TextInputLayout confirmPasswordInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (confirmPasswordInputLayout == null) {
        break missingId;
      }

      id = R.id.emailEditText;
      TextInputEditText emailEditText = ViewBindings.findChildViewById(rootView, id);
      if (emailEditText == null) {
        break missingId;
      }

      id = R.id.emailInputLayout;
      TextInputLayout emailInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (emailInputLayout == null) {
        break missingId;
      }

      id = R.id.loginButton;
      Button loginButton = ViewBindings.findChildViewById(rootView, id);
      if (loginButton == null) {
        break missingId;
      }

      id = R.id.nameEditText;
      TextInputEditText nameEditText = ViewBindings.findChildViewById(rootView, id);
      if (nameEditText == null) {
        break missingId;
      }

      id = R.id.nameInputLayout;
      TextInputLayout nameInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (nameInputLayout == null) {
        break missingId;
      }

      id = R.id.passwordEditText;
      TextInputEditText passwordEditText = ViewBindings.findChildViewById(rootView, id);
      if (passwordEditText == null) {
        break missingId;
      }

      id = R.id.passwordInputLayout;
      TextInputLayout passwordInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (passwordInputLayout == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.registerButton;
      Button registerButton = ViewBindings.findChildViewById(rootView, id);
      if (registerButton == null) {
        break missingId;
      }

      id = R.id.roleDropdown;
      AutoCompleteTextView roleDropdown = ViewBindings.findChildViewById(rootView, id);
      if (roleDropdown == null) {
        break missingId;
      }

      id = R.id.roleInputLayout;
      TextInputLayout roleInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (roleInputLayout == null) {
        break missingId;
      }

      id = R.id.titleTextView;
      TextView titleTextView = ViewBindings.findChildViewById(rootView, id);
      if (titleTextView == null) {
        break missingId;
      }

      return new FragmentRegisterBinding((ConstraintLayout) rootView, confirmPasswordEditText,
          confirmPasswordInputLayout, emailEditText, emailInputLayout, loginButton, nameEditText,
          nameInputLayout, passwordEditText, passwordInputLayout, progressBar, registerButton,
          roleDropdown, roleInputLayout, titleTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
