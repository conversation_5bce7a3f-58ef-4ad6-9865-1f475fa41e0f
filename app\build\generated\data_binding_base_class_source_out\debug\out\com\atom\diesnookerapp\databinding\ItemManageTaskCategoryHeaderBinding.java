// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemManageTaskCategoryHeaderBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView categoryHeaderExpandIcon;

  @NonNull
  public final TextView categoryHeaderNameTextView;

  private ItemManageTaskCategoryHeaderBinding(@NonNull LinearLayout rootView,
      @NonNull ImageView categoryHeaderExpandIcon, @NonNull TextView categoryHeaderNameTextView) {
    this.rootView = rootView;
    this.categoryHeaderExpandIcon = categoryHeaderExpandIcon;
    this.categoryHeaderNameTextView = categoryHeaderNameTextView;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemManageTaskCategoryHeaderBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemManageTaskCategoryHeaderBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_manage_task_category_header, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemManageTaskCategoryHeaderBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.categoryHeaderExpandIcon;
      ImageView categoryHeaderExpandIcon = ViewBindings.findChildViewById(rootView, id);
      if (categoryHeaderExpandIcon == null) {
        break missingId;
      }

      id = R.id.categoryHeaderNameTextView;
      TextView categoryHeaderNameTextView = ViewBindings.findChildViewById(rootView, id);
      if (categoryHeaderNameTextView == null) {
        break missingId;
      }

      return new ItemManageTaskCategoryHeaderBinding((LinearLayout) rootView,
          categoryHeaderExpandIcon, categoryHeaderNameTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
