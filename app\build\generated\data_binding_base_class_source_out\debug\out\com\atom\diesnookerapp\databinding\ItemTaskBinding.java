// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTaskBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final MaterialCardView taskCard;

  @NonNull
  public final CheckBox taskCheckbox;

  @NonNull
  public final TextView taskDescription;

  @NonNull
  public final TextView taskFrequency;

  @NonNull
  public final TextView taskPoints;

  @NonNull
  public final TextView taskTitle;

  private ItemTaskBinding(@NonNull MaterialCardView rootView, @NonNull MaterialCardView taskCard,
      @NonNull CheckBox taskCheckbox, @NonNull TextView taskDescription,
      @NonNull TextView taskFrequency, @NonNull TextView taskPoints, @NonNull TextView taskTitle) {
    this.rootView = rootView;
    this.taskCard = taskCard;
    this.taskCheckbox = taskCheckbox;
    this.taskDescription = taskDescription;
    this.taskFrequency = taskFrequency;
    this.taskPoints = taskPoints;
    this.taskTitle = taskTitle;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTaskBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTaskBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_task, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTaskBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      MaterialCardView taskCard = (MaterialCardView) rootView;

      id = R.id.taskCheckbox;
      CheckBox taskCheckbox = ViewBindings.findChildViewById(rootView, id);
      if (taskCheckbox == null) {
        break missingId;
      }

      id = R.id.taskDescription;
      TextView taskDescription = ViewBindings.findChildViewById(rootView, id);
      if (taskDescription == null) {
        break missingId;
      }

      id = R.id.taskFrequency;
      TextView taskFrequency = ViewBindings.findChildViewById(rootView, id);
      if (taskFrequency == null) {
        break missingId;
      }

      id = R.id.taskPoints;
      TextView taskPoints = ViewBindings.findChildViewById(rootView, id);
      if (taskPoints == null) {
        break missingId;
      }

      id = R.id.taskTitle;
      TextView taskTitle = ViewBindings.findChildViewById(rootView, id);
      if (taskTitle == null) {
        break missingId;
      }

      return new ItemTaskBinding((MaterialCardView) rootView, taskCard, taskCheckbox,
          taskDescription, taskFrequency, taskPoints, taskTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
