// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogManualTimeEntryBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final EditText edittextManualHours;

  @NonNull
  public final EditText edittextManualMinutes;

  private DialogManualTimeEntryBinding(@NonNull LinearLayout rootView,
      @NonNull EditText edittextManualHours, @NonNull EditText edittextManualMinutes) {
    this.rootView = rootView;
    this.edittextManualHours = edittextManualHours;
    this.edittextManualMinutes = edittextManualMinutes;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogManualTimeEntryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogManualTimeEntryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_manual_time_entry, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogManualTimeEntryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.edittext_manual_hours;
      EditText edittextManualHours = ViewBindings.findChildViewById(rootView, id);
      if (edittextManualHours == null) {
        break missingId;
      }

      id = R.id.edittext_manual_minutes;
      EditText edittextManualMinutes = ViewBindings.findChildViewById(rootView, id);
      if (edittextManualMinutes == null) {
        break missingId;
      }

      return new DialogManualTimeEntryBinding((LinearLayout) rootView, edittextManualHours,
          edittextManualMinutes);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
