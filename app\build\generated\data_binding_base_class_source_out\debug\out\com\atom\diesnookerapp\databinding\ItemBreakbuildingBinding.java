// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemBreakbuildingBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView averageTimeText;

  @NonNull
  public final MaterialCardView cardView;

  @NonNull
  public final TextView titleText;

  private ItemBreakbuildingBinding(@NonNull MaterialCardView rootView,
      @NonNull TextView averageTimeText, @NonNull MaterialCardView cardView,
      @NonNull TextView titleText) {
    this.rootView = rootView;
    this.averageTimeText = averageTimeText;
    this.cardView = cardView;
    this.titleText = titleText;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemBreakbuildingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemBreakbuildingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_breakbuilding, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemBreakbuildingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.averageTimeText;
      TextView averageTimeText = ViewBindings.findChildViewById(rootView, id);
      if (averageTimeText == null) {
        break missingId;
      }

      MaterialCardView cardView = (MaterialCardView) rootView;

      id = R.id.titleText;
      TextView titleText = ViewBindings.findChildViewById(rootView, id);
      if (titleText == null) {
        break missingId;
      }

      return new ItemBreakbuildingBinding((MaterialCardView) rootView, averageTimeText, cardView,
          titleText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
