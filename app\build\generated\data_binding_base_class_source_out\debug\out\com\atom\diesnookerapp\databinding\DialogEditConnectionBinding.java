// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogEditConnectionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final CheckBox exerciseAccessCheckbox;

  @NonNull
  public final CheckBox selfAssessmentAccessCheckbox;

  private DialogEditConnectionBinding(@NonNull LinearLayout rootView,
      @NonNull CheckBox exerciseAccessCheckbox, @NonNull CheckBox selfAssessmentAccessCheckbox) {
    this.rootView = rootView;
    this.exerciseAccessCheckbox = exerciseAccessCheckbox;
    this.selfAssessmentAccessCheckbox = selfAssessmentAccessCheckbox;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogEditConnectionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogEditConnectionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_edit_connection, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogEditConnectionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.exerciseAccessCheckbox;
      CheckBox exerciseAccessCheckbox = ViewBindings.findChildViewById(rootView, id);
      if (exerciseAccessCheckbox == null) {
        break missingId;
      }

      id = R.id.selfAssessmentAccessCheckbox;
      CheckBox selfAssessmentAccessCheckbox = ViewBindings.findChildViewById(rootView, id);
      if (selfAssessmentAccessCheckbox == null) {
        break missingId;
      }

      return new DialogEditConnectionBinding((LinearLayout) rootView, exerciseAccessCheckbox,
          selfAssessmentAccessCheckbox);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
