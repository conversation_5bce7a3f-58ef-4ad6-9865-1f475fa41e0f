// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentTrainingsplanHistoryBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView titleText;

  @NonNull
  public final RecyclerView trainingsplanHistoryRecyclerView;

  private FragmentTrainingsplanHistoryBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView titleText, @NonNull RecyclerView trainingsplanHistoryRecyclerView) {
    this.rootView = rootView;
    this.titleText = titleText;
    this.trainingsplanHistoryRecyclerView = trainingsplanHistoryRecyclerView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentTrainingsplanHistoryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentTrainingsplanHistoryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_trainingsplan_history, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentTrainingsplanHistoryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.titleText;
      TextView titleText = ViewBindings.findChildViewById(rootView, id);
      if (titleText == null) {
        break missingId;
      }

      id = R.id.trainingsplanHistoryRecyclerView;
      RecyclerView trainingsplanHistoryRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (trainingsplanHistoryRecyclerView == null) {
        break missingId;
      }

      return new FragmentTrainingsplanHistoryBinding((ConstraintLayout) rootView, titleText,
          trainingsplanHistoryRecyclerView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
