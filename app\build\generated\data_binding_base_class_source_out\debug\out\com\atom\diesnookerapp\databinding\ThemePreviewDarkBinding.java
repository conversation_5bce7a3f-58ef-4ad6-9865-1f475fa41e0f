// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import com.atom.diesnookerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;

public final class ThemePreviewDarkBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final CardView darkThemePreview;

  private ThemePreviewDarkBinding(@NonNull CardView rootView, @NonNull CardView darkThemePreview) {
    this.rootView = rootView;
    this.darkThemePreview = darkThemePreview;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ThemePreviewDarkBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ThemePreviewDarkBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.theme_preview_dark, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ThemePreviewDarkBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    CardView darkThemePreview = (CardView) rootView;

    return new ThemePreviewDarkBinding((CardView) rootView, darkThemePreview);
  }
}
