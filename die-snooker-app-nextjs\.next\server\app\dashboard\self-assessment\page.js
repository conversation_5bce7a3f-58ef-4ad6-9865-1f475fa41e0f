/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/self-assessment/page";
exports.ids = ["app/dashboard/self-assessment/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fself-assessment%2Fpage&page=%2Fdashboard%2Fself-assessment%2Fpage&appPaths=%2Fdashboard%2Fself-assessment%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fself-assessment%2Fpage.tsx&appDir=D%3A%5CHome%5CDokumente%5CAndroidStudioProjects%5CDieSnookerAppGitHub%5Cdie-snooker-app-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CHome%5CDokumente%5CAndroidStudioProjects%5CDieSnookerAppGitHub%5Cdie-snooker-app-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fself-assessment%2Fpage&page=%2Fdashboard%2Fself-assessment%2Fpage&appPaths=%2Fdashboard%2Fself-assessment%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fself-assessment%2Fpage.tsx&appDir=D%3A%5CHome%5CDokumente%5CAndroidStudioProjects%5CDieSnookerAppGitHub%5Cdie-snooker-app-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CHome%5CDokumente%5CAndroidStudioProjects%5CDieSnookerAppGitHub%5Cdie-snooker-app-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/self-assessment/page.tsx */ \"(rsc)/./src/app/dashboard/self-assessment/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'self-assessment',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module4, \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/self-assessment/page\",\n        pathname: \"/dashboard/self-assessment\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fself-assessment%2Fpage&page=%2Fdashboard%2Fself-assessment%2Fpage&appPaths=%2Fdashboard%2Fself-assessment%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fself-assessment%2Fpage.tsx&appDir=D%3A%5CHome%5CDokumente%5CAndroidStudioProjects%5CDieSnookerAppGitHub%5Cdie-snooker-app-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CHome%5CDokumente%5CAndroidStudioProjects%5CDieSnookerAppGitHub%5Cdie-snooker-app-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNIb21lJTVDJTVDRG9rdW1lbnRlJTVDJTVDQW5kcm9pZFN0dWRpb1Byb2plY3RzJTVDJTVDRGllU25vb2tlckFwcEdpdEh1YiU1QyU1Q2RpZS1zbm9va2VyLWFwcC1uZXh0anMlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDSG9tZSU1QyU1Q0Rva3VtZW50ZSU1QyU1Q0FuZHJvaWRTdHVkaW9Qcm9qZWN0cyU1QyU1Q0RpZVNub29rZXJBcHBHaXRIdWIlNUMlNUNkaWUtc25vb2tlci1hcHAtbmV4dGpzJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0hvbWUlNUMlNUNEb2t1bWVudGUlNUMlNUNBbmRyb2lkU3R1ZGlvUHJvamVjdHMlNUMlNUNEaWVTbm9va2VyQXBwR2l0SHViJTVDJTVDZGllLXNub29rZXItYXBwLW5leHRqcyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNIb21lJTVDJTVDRG9rdW1lbnRlJTVDJTVDQW5kcm9pZFN0dWRpb1Byb2plY3RzJTVDJTVDRGllU25vb2tlckFwcEdpdEh1YiU1QyU1Q2RpZS1zbm9va2VyLWFwcC1uZXh0anMlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNodHRwLWFjY2Vzcy1mYWxsYmFjayU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNIb21lJTVDJTVDRG9rdW1lbnRlJTVDJTVDQW5kcm9pZFN0dWRpb1Byb2plY3RzJTVDJTVDRGllU25vb2tlckFwcEdpdEh1YiU1QyU1Q2RpZS1zbm9va2VyLWFwcC1uZXh0anMlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNIb21lJTVDJTVDRG9rdW1lbnRlJTVDJTVDQW5kcm9pZFN0dWRpb1Byb2plY3RzJTVDJTVDRGllU25vb2tlckFwcEdpdEh1YiU1QyU1Q2RpZS1zbm9va2VyLWFwcC1uZXh0anMlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNIb21lJTVDJTVDRG9rdW1lbnRlJTVDJTVDQW5kcm9pZFN0dWRpb1Byb2plY3RzJTVDJTVDRGllU25vb2tlckFwcEdpdEh1YiU1QyU1Q2RpZS1zbm9va2VyLWFwcC1uZXh0anMlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q21ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNIb21lJTVDJTVDRG9rdW1lbnRlJTVDJTVDQW5kcm9pZFN0dWRpb1Byb2plY3RzJTVDJTVDRGllU25vb2tlckFwcEdpdEh1YiU1QyU1Q2RpZS1zbm9va2VyLWFwcC1uZXh0anMlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBMEw7QUFDMUw7QUFDQSwwT0FBNkw7QUFDN0w7QUFDQSwwT0FBNkw7QUFDN0w7QUFDQSxvUkFBbU47QUFDbk47QUFDQSx3T0FBNEw7QUFDNUw7QUFDQSw0UEFBdU07QUFDdk07QUFDQSxrUUFBME07QUFDMU07QUFDQSxzUUFBMk0iLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXEhvbWVcXFxcRG9rdW1lbnRlXFxcXEFuZHJvaWRTdHVkaW9Qcm9qZWN0c1xcXFxEaWVTbm9va2VyQXBwR2l0SHViXFxcXGRpZS1zbm9va2VyLWFwcC1uZXh0anNcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcSG9tZVxcXFxEb2t1bWVudGVcXFxcQW5kcm9pZFN0dWRpb1Byb2plY3RzXFxcXERpZVNub29rZXJBcHBHaXRIdWJcXFxcZGllLXNub29rZXItYXBwLW5leHRqc1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxIb21lXFxcXERva3VtZW50ZVxcXFxBbmRyb2lkU3R1ZGlvUHJvamVjdHNcXFxcRGllU25vb2tlckFwcEdpdEh1YlxcXFxkaWUtc25vb2tlci1hcHAtbmV4dGpzXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXEhvbWVcXFxcRG9rdW1lbnRlXFxcXEFuZHJvaWRTdHVkaW9Qcm9qZWN0c1xcXFxEaWVTbm9va2VyQXBwR2l0SHViXFxcXGRpZS1zbm9va2VyLWFwcC1uZXh0anNcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxodHRwLWFjY2Vzcy1mYWxsYmFja1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcSG9tZVxcXFxEb2t1bWVudGVcXFxcQW5kcm9pZFN0dWRpb1Byb2plY3RzXFxcXERpZVNub29rZXJBcHBHaXRIdWJcXFxcZGllLXNub29rZXItYXBwLW5leHRqc1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXEhvbWVcXFxcRG9rdW1lbnRlXFxcXEFuZHJvaWRTdHVkaW9Qcm9qZWN0c1xcXFxEaWVTbm9va2VyQXBwR2l0SHViXFxcXGRpZS1zbm9va2VyLWFwcC1uZXh0anNcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxhc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcSG9tZVxcXFxEb2t1bWVudGVcXFxcQW5kcm9pZFN0dWRpb1Byb2plY3RzXFxcXERpZVNub29rZXJBcHBHaXRIdWJcXFxcZGllLXNub29rZXItYXBwLW5leHRqc1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG1ldGFkYXRhXFxcXG1ldGFkYXRhLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxIb21lXFxcXERva3VtZW50ZVxcXFxBbmRyb2lkU3R1ZGlvUHJvamVjdHNcXFxcRGllU25vb2tlckFwcEdpdEh1YlxcXFxkaWUtc25vb2tlci1hcHAtbmV4dGpzXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/AuthContext.tsx */ \"(rsc)/./src/context/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNIb21lJTVDJTVDRG9rdW1lbnRlJTVDJTVDQW5kcm9pZFN0dWRpb1Byb2plY3RzJTVDJTVDRGllU25vb2tlckFwcEdpdEh1YiU1QyU1Q2RpZS1zbm9va2VyLWFwcC1uZXh0anMlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBNkoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXEhvbWVcXFxcRG9rdW1lbnRlXFxcXEFuZHJvaWRTdHVkaW9Qcm9qZWN0c1xcXFxEaWVTbm9va2VyQXBwR2l0SHViXFxcXGRpZS1zbm9va2VyLWFwcC1uZXh0anNcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cself-assessment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cself-assessment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/self-assessment/page.tsx */ \"(rsc)/./src/app/dashboard/self-assessment/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNIb21lJTVDJTVDRG9rdW1lbnRlJTVDJTVDQW5kcm9pZFN0dWRpb1Byb2plY3RzJTVDJTVDRGllU25vb2tlckFwcEdpdEh1YiU1QyU1Q2RpZS1zbm9va2VyLWFwcC1uZXh0anMlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNzZWxmLWFzc2Vzc21lbnQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb01BQTRLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxIb21lXFxcXERva3VtZW50ZVxcXFxBbmRyb2lkU3R1ZGlvUHJvamVjdHNcXFxcRGllU25vb2tlckFwcEdpdEh1YlxcXFxkaWUtc25vb2tlci1hcHAtbmV4dGpzXFxcXHNyY1xcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXHNlbGYtYXNzZXNzbWVudFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cself-assessment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxIb21lXFxEb2t1bWVudGVcXEFuZHJvaWRTdHVkaW9Qcm9qZWN0c1xcRGllU25vb2tlckFwcEdpdEh1YlxcZGllLXNub29rZXItYXBwLW5leHRqc1xcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerAppGitHub\\die-snooker-app-nextjs\\src\\app\\dashboard\\layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/dashboard/self-assessment/page.tsx":
/*!****************************************************!*\
  !*** ./src/app/dashboard/self-assessment/page.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerAppGitHub\\die-snooker-app-nextjs\\src\\app\\dashboard\\self-assessment\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcSG9tZVxcRG9rdW1lbnRlXFxBbmRyb2lkU3R1ZGlvUHJvamVjdHNcXERpZVNub29rZXJBcHBHaXRIdWJcXGRpZS1zbm9va2VyLWFwcC1uZXh0anNcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcxOWNiMGZjM2Y2M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/AuthContext */ \"(rsc)/./src/context/AuthContext.tsx\");\n\n\n\n\n // Import AuthProvider\nconst metadata = {\n    title: \"Die Snooker App\",\n    description: \"Track your snooker progress and connect with trainers.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        children: [\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerAppGitHub\\die-snooker-app-nextjs\\src\\context\\AuthContext.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerAppGitHub\\die-snooker-app-nextjs\\src\\context\\AuthContext.tsx",
"useAuth",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/AuthContext.tsx */ \"(ssr)/./src/context/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Ccontext%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(ssr)/./src/app/dashboard/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNIb21lJTVDJTVDRG9rdW1lbnRlJTVDJTVDQW5kcm9pZFN0dWRpb1Byb2plY3RzJTVDJTVDRGllU25vb2tlckFwcEdpdEh1YiU1QyU1Q2RpZS1zbm9va2VyLWFwcC1uZXh0anMlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBNkoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXEhvbWVcXFxcRG9rdW1lbnRlXFxcXEFuZHJvaWRTdHVkaW9Qcm9qZWN0c1xcXFxEaWVTbm9va2VyQXBwR2l0SHViXFxcXGRpZS1zbm9va2VyLWFwcC1uZXh0anNcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cself-assessment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cself-assessment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/self-assessment/page.tsx */ \"(ssr)/./src/app/dashboard/self-assessment/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNIb21lJTVDJTVDRG9rdW1lbnRlJTVDJTVDQW5kcm9pZFN0dWRpb1Byb2plY3RzJTVDJTVDRGllU25vb2tlckFwcEdpdEh1YiU1QyU1Q2RpZS1zbm9va2VyLWFwcC1uZXh0anMlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNzZWxmLWFzc2Vzc21lbnQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb01BQTRLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxIb21lXFxcXERva3VtZW50ZVxcXFxBbmRyb2lkU3R1ZGlvUHJvamVjdHNcXFxcRGllU25vb2tlckFwcEdpdEh1YlxcXFxkaWUtc25vb2tlci1hcHAtbmV4dGpzXFxcXHNyY1xcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXHNlbGYtYXNzZXNzbWVudFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CHome%5C%5CDokumente%5C%5CAndroidStudioProjects%5C%5CDieSnookerAppGitHub%5C%5Cdie-snooker-app-nextjs%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cself-assessment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PlayerDashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n // To display user info or role\nconst NavLink = ({ href, children })=>{\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const isActive = pathname === href;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n        href: href,\n        className: `block px-4 py-2 rounded-md text-sm font-medium transition-colors\n      ${isActive ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-600 hover:text-white'}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, undefined);\n};\nfunction PlayerDashboardLayout({ children }) {\n    const { currentUser, userProfile } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const handleLogout = async ()=>{\n        try {\n            await _lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth.signOut();\n            router.push('/login');\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        }\n    };\n    // If auth is still loading, or no user, show loading or redirect (handled by withAuth on page level)\n    // This layout assumes it's rendered for an authenticated user.\n    // Adding a basic loading check here for robustness.\n    if (!currentUser && !userProfile) {\n        // This state should ideally be handled by page-level withAuth HOC redirecting.\n        // If somehow layout is shown without user, it might be during brief transition.\n        // Can show a minimal loading or empty state.\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"Loading user...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 45,\n                columnNumber: 77\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 45,\n            columnNumber: 14\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col md:flex-row bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"w-full md:w-64 bg-indigo-800 text-white flex-shrink-0 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-5 border-b border-indigo-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-semibold\",\n                                children: \"Die Snooker App\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this),\n                            userProfile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 text-sm text-indigo-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: userProfile.displayName || currentUser?.email\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"Role: \",\n                                            userProfile.role\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"p-3 space-y-1 flex-grow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                href: \"/dashboard\",\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this),\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                href: \"/dashboard/self-assessment\",\n                                children: \"Selbsteinsch\\xe4tzung\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                href: \"/dashboard/exercises\",\n                                children: \"\\xdcbungen\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                href: \"/dashboard/tasks\",\n                                children: \"Aufgaben\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                href: \"/dashboard/training-plan\",\n                                children: \"Trainingsplan\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                href: \"/dashboard/trainer-connections\",\n                                children: \"Trainer-Verbindungen\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-5 border-t border-indigo-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleLogout,\n                            className: \"w-full bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded text-sm transition-colors\",\n                            children: \"Logout\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 p-6 overflow-auto\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/self-assessment/page.tsx":
/*!****************************************************!*\
  !*** ./src/app/dashboard/self-assessment/page.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_auth_withAuth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/auth/withAuth */ \"(ssr)/./src/components/auth/withAuth.tsx\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react_chartjs_2__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-chartjs-2 */ \"(ssr)/./node_modules/react-chartjs-2/dist/index.js\");\n/* harmony import */ var chart_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! chart.js */ \"(ssr)/./node_modules/chart.js/dist/chart.js\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(ssr)/./src/components/ui/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nchart_js__WEBPACK_IMPORTED_MODULE_7__.Chart.register(chart_js__WEBPACK_IMPORTED_MODULE_7__.CategoryScale, chart_js__WEBPACK_IMPORTED_MODULE_7__.LinearScale, chart_js__WEBPACK_IMPORTED_MODULE_7__.PointElement, chart_js__WEBPACK_IMPORTED_MODULE_7__.LineElement, chart_js__WEBPACK_IMPORTED_MODULE_7__.Title, chart_js__WEBPACK_IMPORTED_MODULE_7__.Tooltip, chart_js__WEBPACK_IMPORTED_MODULE_7__.Legend);\nfunction SelfAssessmentPage() {\n    const { currentUser } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [trainingRecords, setTrainingRecords] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [questionRecords, setQuestionRecords] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"SelfAssessmentPage.useEffect\": ()=>{\n            if (!currentUser) return;\n            const fetchData = {\n                \"SelfAssessmentPage.useEffect.fetchData\": async ()=>{\n                    setLoading(true);\n                    setError(null);\n                    try {\n                        // Fetch Training Records\n                        const trQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"training_records\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.where)(\"userId\", \"==\", currentUser.uid), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.orderBy)(\"lastUpdated\", \"desc\"));\n                        const trSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDocs)(trQuery);\n                        const fetchedTrainingRecords = trSnapshot.docs.map({\n                            \"SelfAssessmentPage.useEffect.fetchData.fetchedTrainingRecords\": (doc)=>({\n                                    id: doc.id,\n                                    ...doc.data()\n                                })\n                        }[\"SelfAssessmentPage.useEffect.fetchData.fetchedTrainingRecords\"]);\n                        setTrainingRecords(fetchedTrainingRecords);\n                        // Fetch Question Records\n                        const qrQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"question_records\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.where)(\"userId\", \"==\", currentUser.uid), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.orderBy)(\"lastUpdated\", \"desc\"));\n                        const qrSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDocs)(qrQuery);\n                        const fetchedQuestionRecords = qrSnapshot.docs.map({\n                            \"SelfAssessmentPage.useEffect.fetchData.fetchedQuestionRecords\": (doc)=>({\n                                    id: doc.id,\n                                    ...doc.data()\n                                })\n                        }[\"SelfAssessmentPage.useEffect.fetchData.fetchedQuestionRecords\"]);\n                        setQuestionRecords(fetchedQuestionRecords);\n                    } catch (err) {\n                        console.error(\"Error fetching self-assessment data:\", err);\n                        setError(\"Failed to load data. \" + err.message);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"SelfAssessmentPage.useEffect.fetchData\"];\n            fetchData();\n        }\n    }[\"SelfAssessmentPage.useEffect\"], [\n        currentUser\n    ]);\n    const trainingChartData = {\n        labels: trainingRecords.map((r)=>new Date(r.date || r.lastUpdated.toDate()).toLocaleDateString('de-DE')).reverse(),\n        datasets: [\n            {\n                label: 'Average Score',\n                data: trainingRecords.map((r)=>{\n                    const totalScore = r.items.reduce((sum, item)=>sum + (item.score || 0), 0);\n                    return r.items.length > 0 ? totalScore / r.items.length : 0;\n                }).reverse(),\n                fill: false,\n                borderColor: 'rgb(75, 192, 192)',\n                tension: 0.1\n            }\n        ]\n    };\n    const chartOptions = {\n        responsive: true,\n        plugins: {\n            legend: {\n                position: 'top'\n            },\n            title: {\n                display: true,\n                text: 'Self-Assessment Score Trends'\n            }\n        },\n        scales: {\n            y: {\n                beginAtZero: true,\n                max: 10 // Assuming scores are out of 10\n            }\n        }\n    };\n    if (loading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\",\n        lineNumber: 125,\n        columnNumber: 23\n    }, this);\n    if (error) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-red-500 p-4 bg-red-100 rounded-md\",\n        children: error\n    }, void 0, false, {\n        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\",\n        lineNumber: 126,\n        columnNumber: 21\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-3xl font-bold mb-6 text-gray-800\",\n                children: \"Selbsteinsch\\xe4tzung\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"mb-8 bg-white p-6 rounded-lg shadow-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-semibold mb-4 text-gray-700\",\n                        children: \"Score Verlauf (Training Records)\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    trainingRecords.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-80\",\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_chartjs_2__WEBPACK_IMPORTED_MODULE_8__.Line, {\n                                options: chartOptions,\n                                data: trainingChartData\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"No training records found to display chart.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 space-y-4\",\n                        children: trainingRecords.map((record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                className: \"p-3 border rounded-md bg-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                        className: \"font-medium cursor-pointer text-gray-700 hover:text-indigo-600\",\n                                        children: [\n                                            record.type,\n                                            \" - \",\n                                            new Date(record.date || record.lastUpdated.toDate()).toLocaleDateString('de-DE')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"mt-2 list-disc list-inside pl-4 text-sm text-gray-600 space-y-1\",\n                                        children: record.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    item.title,\n                                                    \": \",\n                                                    item.score,\n                                                    \"/10\"\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, record.id, true, {\n                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-white p-6 rounded-lg shadow-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-semibold mb-4 text-gray-700\",\n                        children: \"Fragebogen Antworten (Question Records)\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, this),\n                    questionRecords.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: questionRecords.map((record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                className: \"p-3 border rounded-md bg-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                        className: \"font-medium cursor-pointer text-gray-700 hover:text-indigo-600\",\n                                        children: [\n                                            record.type,\n                                            \" - \",\n                                            new Date(record.date || record.lastUpdated.toDate()).toLocaleDateString('de-DE')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"mt-2 list-disc list-inside pl-4 text-sm text-gray-600 space-y-1\",\n                                        children: record.questions.map((q, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: [\n                                                            q.title,\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    \" \",\n                                                    q.answer\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, record.id, true, {\n                                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"No question records found.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\app\\\\dashboard\\\\self-assessment\\\\page.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,_components_auth_withAuth__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(SelfAssessmentPage, {\n    redirectTo: '/login'\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/self-assessment/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/withAuth.tsx":
/*!******************************************!*\
  !*** ./src/components/auth/withAuth.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(ssr)/./src/components/ui/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n // We'll create this next\nfunction withAuth(WrappedComponent, options = {}) {\n    const WithAuthComponent = (props)=>{\n        const { currentUser, userProfile, loading } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n        const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n        const { roles, redirectTo } = options;\n        (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n            \"withAuth.WithAuthComponent.useEffect\": ()=>{\n                if (loading) {\n                    return; // Wait for auth state to load\n                }\n                if (!currentUser) {\n                    router.replace(redirectTo || '/login'); // Default to player login\n                    return;\n                }\n                // Role check if roles are specified\n                if (roles && roles.length > 0) {\n                    if (!userProfile || !roles.includes(userProfile.role)) {\n                        // If roles are specified and user doesn't have one, redirect.\n                        // This could be to a generic access denied page or back to their default dashboard/login.\n                        // For simplicity, redirecting to player login if trainer role check fails.\n                        router.replace(redirectTo || (userProfile?.role === 'PLAYER' ? '/dashboard' : '/login'));\n                    }\n                }\n            }\n        }[\"withAuth.WithAuthComponent.useEffect\"], [\n            currentUser,\n            userProfile,\n            loading,\n            router,\n            roles,\n            redirectTo\n        ]);\n        if (loading || !currentUser) {\n            // Show a loading spinner or a blank page while checking auth\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\components\\\\auth\\\\withAuth.tsx\",\n                lineNumber: 46,\n                columnNumber: 14\n            }, this);\n        }\n        // Additional role check before rendering the component, in case useEffect hasn't run yet or for strictness\n        if (roles && roles.length > 0 && (!userProfile || !roles.includes(userProfile.role))) {\n            // This check ensures that if the user is logged in but doesn't have the required role,\n            // they don't briefly see the component content.\n            // It might be redundant if useEffect handles redirection quickly, but adds robustness.\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\components\\\\auth\\\\withAuth.tsx\",\n                lineNumber: 54,\n                columnNumber: 16\n            }, this); // Or an \"Access Denied\" component\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WrappedComponent, {\n            ...props\n        }, void 0, false, {\n            fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\components\\\\auth\\\\withAuth.tsx\",\n            lineNumber: 58,\n            columnNumber: 12\n        }, this);\n    };\n    // Set a display name for the HOC for better debugging\n    WithAuthComponent.displayName = `WithAuth(${WrappedComponent.displayName || WrappedComponent.name || 'Component'})`;\n    return WithAuthComponent;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/withAuth.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/LoadingSpinner.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/LoadingSpinner.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoadingSpinner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction LoadingSpinner() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center min-h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-indigo-600\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9Mb2FkaW5nU3Bpbm5lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUVlLFNBQVNBO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs7Ozs7Ozs7OztBQUdyQiIsInNvdXJjZXMiOlsiRDpcXEhvbWVcXERva3VtZW50ZVxcQW5kcm9pZFN0dWRpb1Byb2plY3RzXFxEaWVTbm9va2VyQXBwR2l0SHViXFxkaWUtc25vb2tlci1hcHAtbmV4dGpzXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxMb2FkaW5nU3Bpbm5lci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmdTcGlubmVyKCkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWluLWgtc2NyZWVuXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0xNiB3LTE2IGJvcmRlci10LTQgYm9yZGVyLWItNCBib3JkZXItaW5kaWdvLTYwMFwiPjwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkxvYWRpbmdTcGlubmVyIiwiZGl2IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/LoadingSpinner.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [currentUser, setCurrentUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, {\n                \"AuthProvider.useEffect.unsubscribe\": async (user)=>{\n                    setLoading(true);\n                    setError(null);\n                    setCurrentUser(user);\n                    setUserProfile(null); // Reset profile on auth state change\n                    if (user) {\n                        try {\n                            const userProfileRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"user_profiles\", user.uid);\n                            const userProfileSnap = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)(userProfileRef);\n                            if (userProfileSnap.exists()) {\n                                setUserProfile(userProfileSnap.data());\n                            } else {\n                                // This case should ideally be handled at login/registration\n                                // For example, creating a default profile if one doesn't exist.\n                                console.warn(\"User profile does not exist for UID:\", user.uid);\n                            }\n                        } catch (e) {\n                            console.error(\"Error fetching user profile:\", e);\n                            setError(e);\n                        }\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.unsubscribe\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>unsubscribe()\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            currentUser,\n            userProfile,\n            loading,\n            error\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Home\\\\Dokumente\\\\AndroidStudioProjects\\\\DieSnookerAppGitHub\\\\die-snooker-app-nextjs\\\\src\\\\context\\\\AuthContext.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/context/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   app: () => (/* binding */ app),\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(ssr)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyB-fFmAiherpGg3dbecT3v_Z-368kSEMPY\",\n    authDomain: \"die-snooker-app.firebaseapp.com\",\n    projectId: \"die-snooker-app\",\n    storageBucket: \"die-snooker-app.firebasestorage.app\",\n    messagingSenderId: \"547283642216\",\n    appId: \"1:547283642216:web:7f2fdc23dab5ce8430d8dd\",\n    measurementId: \"G-GTFVZZ4LJ\"\n};\nlet app;\nlet auth;\nlet db;\nif ((0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length === 0) {\n    app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n} else {\n    app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)()[0];\n}\nauth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\ndb = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@firebase","vendor-chunks/@grpc","vendor-chunks/protobufjs","vendor-chunks/long","vendor-chunks/@protobufjs","vendor-chunks/lodash.camelcase","vendor-chunks/tslib","vendor-chunks/idb","vendor-chunks/@swc","vendor-chunks/firebase","vendor-chunks/chart.js","vendor-chunks/@kurkle","vendor-chunks/react-chartjs-2"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fself-assessment%2Fpage&page=%2Fdashboard%2Fself-assessment%2Fpage&appPaths=%2Fdashboard%2Fself-assessment%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fself-assessment%2Fpage.tsx&appDir=D%3A%5CHome%5CDokumente%5CAndroidStudioProjects%5CDieSnookerAppGitHub%5Cdie-snooker-app-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CHome%5CDokumente%5CAndroidStudioProjects%5CDieSnookerAppGitHub%5Cdie-snooker-app-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();