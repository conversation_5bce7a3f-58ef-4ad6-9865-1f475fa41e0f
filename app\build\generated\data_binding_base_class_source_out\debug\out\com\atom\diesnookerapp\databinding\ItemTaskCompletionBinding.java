// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTaskCompletionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView completionText;

  @NonNull
  public final TextView pointsText;

  @NonNull
  public final TextView taskTitleText;

  private ItemTaskCompletionBinding(@NonNull LinearLayout rootView,
      @NonNull TextView completionText, @NonNull TextView pointsText,
      @NonNull TextView taskTitleText) {
    this.rootView = rootView;
    this.completionText = completionText;
    this.pointsText = pointsText;
    this.taskTitleText = taskTitleText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTaskCompletionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTaskCompletionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_task_completion, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTaskCompletionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.completionText;
      TextView completionText = ViewBindings.findChildViewById(rootView, id);
      if (completionText == null) {
        break missingId;
      }

      id = R.id.pointsText;
      TextView pointsText = ViewBindings.findChildViewById(rootView, id);
      if (pointsText == null) {
        break missingId;
      }

      id = R.id.taskTitleText;
      TextView taskTitleText = ViewBindings.findChildViewById(rootView, id);
      if (taskTitleText == null) {
        break missingId;
      }

      return new ItemTaskCompletionBinding((LinearLayout) rootView, completionText, pointsText,
          taskTitleText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
