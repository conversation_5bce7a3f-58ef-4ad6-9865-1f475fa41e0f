// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTaskHistoryBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final LinearLayout contentLayout;

  @NonNull
  public final TextView dateRangeText;

  @NonNull
  public final ImageView expandCollapseIcon;

  @NonNull
  public final LinearLayout headerLayout;

  @NonNull
  public final MaterialCardView historyCard;

  @NonNull
  public final RecyclerView taskCompletionsRecyclerView;

  @NonNull
  public final TextView totalPointsText;

  @NonNull
  public final TextView weekText;

  private ItemTaskHistoryBinding(@NonNull MaterialCardView rootView,
      @NonNull LinearLayout contentLayout, @NonNull TextView dateRangeText,
      @NonNull ImageView expandCollapseIcon, @NonNull LinearLayout headerLayout,
      @NonNull MaterialCardView historyCard, @NonNull RecyclerView taskCompletionsRecyclerView,
      @NonNull TextView totalPointsText, @NonNull TextView weekText) {
    this.rootView = rootView;
    this.contentLayout = contentLayout;
    this.dateRangeText = dateRangeText;
    this.expandCollapseIcon = expandCollapseIcon;
    this.headerLayout = headerLayout;
    this.historyCard = historyCard;
    this.taskCompletionsRecyclerView = taskCompletionsRecyclerView;
    this.totalPointsText = totalPointsText;
    this.weekText = weekText;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTaskHistoryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTaskHistoryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_task_history, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTaskHistoryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.contentLayout;
      LinearLayout contentLayout = ViewBindings.findChildViewById(rootView, id);
      if (contentLayout == null) {
        break missingId;
      }

      id = R.id.dateRangeText;
      TextView dateRangeText = ViewBindings.findChildViewById(rootView, id);
      if (dateRangeText == null) {
        break missingId;
      }

      id = R.id.expandCollapseIcon;
      ImageView expandCollapseIcon = ViewBindings.findChildViewById(rootView, id);
      if (expandCollapseIcon == null) {
        break missingId;
      }

      id = R.id.headerLayout;
      LinearLayout headerLayout = ViewBindings.findChildViewById(rootView, id);
      if (headerLayout == null) {
        break missingId;
      }

      MaterialCardView historyCard = (MaterialCardView) rootView;

      id = R.id.taskCompletionsRecyclerView;
      RecyclerView taskCompletionsRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (taskCompletionsRecyclerView == null) {
        break missingId;
      }

      id = R.id.totalPointsText;
      TextView totalPointsText = ViewBindings.findChildViewById(rootView, id);
      if (totalPointsText == null) {
        break missingId;
      }

      id = R.id.weekText;
      TextView weekText = ViewBindings.findChildViewById(rootView, id);
      if (weekText == null) {
        break missingId;
      }

      return new ItemTaskHistoryBinding((MaterialCardView) rootView, contentLayout, dateRangeText,
          expandCollapseIcon, headerLayout, historyCard, taskCompletionsRecyclerView,
          totalPointsText, weekText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
