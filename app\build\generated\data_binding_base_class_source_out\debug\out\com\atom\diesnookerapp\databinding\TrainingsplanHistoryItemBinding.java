// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class TrainingsplanHistoryItemBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView completionStatsTextView;

  @NonNull
  public final LinearLayout contentLayout;

  @NonNull
  public final ImageView expandCollapseIcon;

  @NonNull
  public final LinearLayout headerLayout;

  @NonNull
  public final TextView itemsTextView;

  @NonNull
  public final TextView weekTextView;

  private TrainingsplanHistoryItemBinding(@NonNull MaterialCardView rootView,
      @NonNull TextView completionStatsTextView, @NonNull LinearLayout contentLayout,
      @NonNull ImageView expandCollapseIcon, @NonNull LinearLayout headerLayout,
      @NonNull TextView itemsTextView, @NonNull TextView weekTextView) {
    this.rootView = rootView;
    this.completionStatsTextView = completionStatsTextView;
    this.contentLayout = contentLayout;
    this.expandCollapseIcon = expandCollapseIcon;
    this.headerLayout = headerLayout;
    this.itemsTextView = itemsTextView;
    this.weekTextView = weekTextView;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static TrainingsplanHistoryItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static TrainingsplanHistoryItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.trainingsplan_history_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static TrainingsplanHistoryItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.completionStatsTextView;
      TextView completionStatsTextView = ViewBindings.findChildViewById(rootView, id);
      if (completionStatsTextView == null) {
        break missingId;
      }

      id = R.id.contentLayout;
      LinearLayout contentLayout = ViewBindings.findChildViewById(rootView, id);
      if (contentLayout == null) {
        break missingId;
      }

      id = R.id.expandCollapseIcon;
      ImageView expandCollapseIcon = ViewBindings.findChildViewById(rootView, id);
      if (expandCollapseIcon == null) {
        break missingId;
      }

      id = R.id.headerLayout;
      LinearLayout headerLayout = ViewBindings.findChildViewById(rootView, id);
      if (headerLayout == null) {
        break missingId;
      }

      id = R.id.itemsTextView;
      TextView itemsTextView = ViewBindings.findChildViewById(rootView, id);
      if (itemsTextView == null) {
        break missingId;
      }

      id = R.id.weekTextView;
      TextView weekTextView = ViewBindings.findChildViewById(rootView, id);
      if (weekTextView == null) {
        break missingId;
      }

      return new TrainingsplanHistoryItemBinding((MaterialCardView) rootView,
          completionStatsTextView, contentLayout, expandCollapseIcon, headerLayout, itemsTextView,
          weekTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
