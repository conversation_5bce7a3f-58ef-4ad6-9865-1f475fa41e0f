// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentExerciseDetailStellungsspielBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton addTimeButton;

  @NonNull
  public final TextView currentColumnText;

  @NonNull
  public final MaterialButton missButton;

  @NonNull
  public final TextView scores1Text;

  @NonNull
  public final TextView scores2Text;

  @NonNull
  public final TextView scores3Text;

  @NonNull
  public final TextView scores4Text;

  @NonNull
  public final TextView scores5Text;

  @NonNull
  public final TextView scores6Text;

  @NonNull
  public final MaterialButton showGraphButton;

  @NonNull
  public final MaterialButton successButton;

  @NonNull
  public final TextView titleText;

  private FragmentExerciseDetailStellungsspielBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton addTimeButton, @NonNull TextView currentColumnText,
      @NonNull MaterialButton missButton, @NonNull TextView scores1Text,
      @NonNull TextView scores2Text, @NonNull TextView scores3Text, @NonNull TextView scores4Text,
      @NonNull TextView scores5Text, @NonNull TextView scores6Text,
      @NonNull MaterialButton showGraphButton, @NonNull MaterialButton successButton,
      @NonNull TextView titleText) {
    this.rootView = rootView;
    this.addTimeButton = addTimeButton;
    this.currentColumnText = currentColumnText;
    this.missButton = missButton;
    this.scores1Text = scores1Text;
    this.scores2Text = scores2Text;
    this.scores3Text = scores3Text;
    this.scores4Text = scores4Text;
    this.scores5Text = scores5Text;
    this.scores6Text = scores6Text;
    this.showGraphButton = showGraphButton;
    this.successButton = successButton;
    this.titleText = titleText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentExerciseDetailStellungsspielBinding inflate(
      @NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentExerciseDetailStellungsspielBinding inflate(
      @NonNull LayoutInflater inflater, @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_exercise_detail_stellungsspiel, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentExerciseDetailStellungsspielBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addTimeButton;
      MaterialButton addTimeButton = ViewBindings.findChildViewById(rootView, id);
      if (addTimeButton == null) {
        break missingId;
      }

      id = R.id.currentColumnText;
      TextView currentColumnText = ViewBindings.findChildViewById(rootView, id);
      if (currentColumnText == null) {
        break missingId;
      }

      id = R.id.missButton;
      MaterialButton missButton = ViewBindings.findChildViewById(rootView, id);
      if (missButton == null) {
        break missingId;
      }

      id = R.id.scores1Text;
      TextView scores1Text = ViewBindings.findChildViewById(rootView, id);
      if (scores1Text == null) {
        break missingId;
      }

      id = R.id.scores2Text;
      TextView scores2Text = ViewBindings.findChildViewById(rootView, id);
      if (scores2Text == null) {
        break missingId;
      }

      id = R.id.scores3Text;
      TextView scores3Text = ViewBindings.findChildViewById(rootView, id);
      if (scores3Text == null) {
        break missingId;
      }

      id = R.id.scores4Text;
      TextView scores4Text = ViewBindings.findChildViewById(rootView, id);
      if (scores4Text == null) {
        break missingId;
      }

      id = R.id.scores5Text;
      TextView scores5Text = ViewBindings.findChildViewById(rootView, id);
      if (scores5Text == null) {
        break missingId;
      }

      id = R.id.scores6Text;
      TextView scores6Text = ViewBindings.findChildViewById(rootView, id);
      if (scores6Text == null) {
        break missingId;
      }

      id = R.id.showGraphButton;
      MaterialButton showGraphButton = ViewBindings.findChildViewById(rootView, id);
      if (showGraphButton == null) {
        break missingId;
      }

      id = R.id.successButton;
      MaterialButton successButton = ViewBindings.findChildViewById(rootView, id);
      if (successButton == null) {
        break missingId;
      }

      id = R.id.titleText;
      TextView titleText = ViewBindings.findChildViewById(rootView, id);
      if (titleText == null) {
        break missingId;
      }

      return new FragmentExerciseDetailStellungsspielBinding((LinearLayout) rootView, addTimeButton,
          currentColumnText, missButton, scores1Text, scores2Text, scores3Text, scores4Text,
          scores5Text, scores6Text, showGraphButton, successButton, titleText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
