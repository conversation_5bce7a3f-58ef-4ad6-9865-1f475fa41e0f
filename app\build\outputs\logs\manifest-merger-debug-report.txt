-- Merging decision tree log ---
manifest
ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:2:1-47:12
INJECTED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:2:1-47:12
INJECTED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:2:1-47:12
INJECTED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:2:1-47:12
MERGED from [androidx.databinding:viewbinding:8.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8a06c949cfa652a611f2acfa10a87be\transformed\viewbinding-8.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29b36062e5fe2b6a9996eb16f4854927\transformed\navigation-common-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7eb0b971608d67ff1f47a47c81bdc2c0\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f2b5a48b3fe5567547a5485090d7705\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e20a0b3acc200aaf9aba33ac523d0336\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b085cf425b5208770b1733a512f3bf9\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85b578f36c5215c705f2b8fa39faf60e\transformed\navigation-fragment-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14ba56137749021c9a2be664170d7ac7\transformed\navigation-ui-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b064642397aead0c4b677d2bb09b1c2b\transformed\navigation-ui-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b903733d615c0b2ff05f9b9df539ba65\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62593010688224dfc5b372405b080b58\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cec108e13a9d083e64db08e1b6145385\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\edf207558568566ef6df89a74527d120\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3063590ab377558e02f95f2978ee188b\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\545b8561523e2b1f4d0d7838087a8a04\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0c3da69700d8dfa40aae4cd8e12e270\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f0852f393a02041fb6485b4bdfdaba16\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f935ad6ff8f4a372e6e4c3926f45186a\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ddfac4953b40634319c2ee646316b01\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\342fc164069752ca474125786d028d25\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3345a7fbe747144276fa9999773cd59e\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb53497875ff465f7931d65e5429b5b8\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d4b7da40d5e42e728aab7fb52f7c68c\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b415f8502827851acc5588034aaa524f\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\746f9ea1c7e141b7cd1c54c6bb6bb0fa\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\630a6b64cf0b99c94039eb94430e9039\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2965a251595db0b0aea7828df95d2c22\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4d885512c0beef2ec8aea2204c9f1c7\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13b61402139f7407d176cf88d856f408\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e8c4906aeb6063dad76cbfa2a875519\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f80cdb95ae6a2948d9ffde64c419845\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1807240b62a7a8a8286290202eecc98f\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f1c5947f95698f6a281d724a14c88cf\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:17:1-75:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfd8ce3ca23c7f1b2cd539ccd756cc0a\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c176bc85a783f4b95615ae4634f8ae2f\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c4f5c5e1a3cc63e3269d5bb878123ce\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d8af47904ad7f9485d00657e6341c9f\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb90d5889eaa6b542282a5020f72f14d\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a811e7d3a67ee70560667c9e624d0050\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:15:1-25:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39e6ae3b5b803b311daf42021a91c950\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:17.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a24007aeddf495323f15817b62f08e3\transformed\play-services-auth-api-phone-17.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f74f887e8617a184c260b963fe8585b\transformed\play-services-base-18.0.1\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d710c6c42ffe0a6fb77c1916e456f480\transformed\recaptcha-18.1.2\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9228a8b7e1895d8104ec3da5ca11dea2\transformed\integrity-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.work:work-runtime-ktx:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\476165fc5437857fd89974625a791c5a\transformed\work-runtime-ktx-2.10.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58dea54d1ca4f9d9a165d72ef873c4cc\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3477c63391d771930e606dbf0a8d42a5\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71de2404ef70d4038d75f48e1378fd1f\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2cc96fae3303c9b9ee2713056a6ecbd\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbe01b0c014df9a20d7ee5e1186f3b22\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\203bebdaf46ee5720eee11b108418f1d\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1078977e84cf2f81e9b64ac9497bef28\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d4b274c36eaeab0b07fee59e023b9ae\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3cb10dbac82f1f5fa7d5347ce82a4e1\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2e672e89faeb26611b2eb615ced0995\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e188628e5386314399d09afe6f3689d\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19a97e95153f937a5eea3530d904e77a\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71ed0c9230eab0bc603dcc68cfab7b18\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91a05db1f70fcc1560e99a1a2e5a1e0b\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5463204cc3b6345dbf5562ab6d7cc40d\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c2724dca3c98e276efaab131df51d3a\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2daf4fa1f3706cd82523bfe6089ca9b2\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960c7054b802f15a0b75b85e164cffa8\transformed\core-1.15.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acfd5b96537540834722577deee275fe\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39d3ae8d4a330c67607b288e5601c24e\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f06ba6d15bb51653c2986f9faea87ba3\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\323fccee1568262fa96249e1496cc48f\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2922db0595fb1e5606cfbb7d37671928\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\907836426769673d6f72fd6688ee3b88\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b1fbaf21c106f939b47d187adb1c0fac\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67e1c84be8318a5402cb5635d76c8e98\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72cd04d57cf61f97c873831b15834ca5\transformed\lifecycle-service-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\095d12f777f8d88c9b4836652b403fa4\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\220e66dfbaf79c535786699abfffffd8\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62fbe1e14622130bc6e17cd2544681b7\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61951ba496fe13479e7e43963c62b9cb\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91aefd5272af96834250923d7b3d532b\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c6802c9b562899c207089fd57e4a1fc\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a5b100d33267656477d10d382e016a1\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4e6d9afd0ba598fc2f0f129d06b1e7b\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abae32c1600cfb9e911ec112f910fe8b\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92e8c801482673d28198346e411930c4\transformed\play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6254bcc2d81321e92f6d0c196b1d48d\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c3f9c036a4b978d45aa46e441d17c9d\transformed\activity-1.10.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d844f6e44315539624fbb7ebb020ef4d\transformed\activity-ktx-1.10.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92450d02dfb2767d0073e6e1885d63e5\transformed\activity-compose-1.10.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e380358256c1e9482b3d8f26b625fdd2\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\755be06f9b0e6e8f28aaf6e4fbac3477\transformed\core-ktx-1.15.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f89e2b54c583c2b631f13d7a4065b5b\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:2:1-20:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\082660c5f01ce2062ab5bf888cdcb292\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a75f9814effd09ab0114ecc4773eaaa3\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\075c5acae7e5deae36206802f62148b0\transformed\firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6139beaedbf45a3dbb9e7c49f83721e\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4ae6dc4e7b1dfb5b9ce574b1ad21a1c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a56bf67678845e6b8d8185b57b0bcaff\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3615514103634996edc945592e2d2f6c\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\137bc933d4e1f9a7501d7d0f059fce7a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d4690ed394e54ec95578d84de51d293\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83dd44f1faea239aa84074537b53dae5\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb23ff2ed1153403db45fdc7c6a07b36\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08800e8822abfbb6c6677901ff35004e\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a08d0afea0fb8d04257f17bda2a4020b\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adc8c372d1b4d5985567fdce3c1ff1a0\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0720eb107e89c923d3bf183f803b709a\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3805e241e78cd74e1da024359e9cf97\transformed\grpc-android-1.52.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.jakewharton.threetenabp:threetenabp:1.4.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0d4291afdc56b4fa9aca61e2d53cf48\transformed\threetenabp-1.4.6\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba3fcf8cb7800215380828f3c58fc8c\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:6:5-77
	android:name
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:6:22-74
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:8:5-81
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:25:5-81
	android:name
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:8:22-78
uses-permission#android.permission.INTERNET
ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:10:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c4f5c5e1a3cc63e3269d5bb878123ce\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c4f5c5e1a3cc63e3269d5bb878123ce\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d710c6c42ffe0a6fb77c1916e456f480\transformed\recaptcha-18.1.2\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d710c6c42ffe0a6fb77c1916e456f480\transformed\recaptcha-18.1.2\AndroidManifest.xml:7:5-67
	android:name
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:10:22-64
application
ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:12:5-45:19
INJECTED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:12:5-45:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b903733d615c0b2ff05f9b9df539ba65\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b903733d615c0b2ff05f9b9df539ba65\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62593010688224dfc5b372405b080b58\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62593010688224dfc5b372405b080b58\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4d885512c0beef2ec8aea2204c9f1c7\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4d885512c0beef2ec8aea2204c9f1c7\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f80cdb95ae6a2948d9ffde64c419845\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f80cdb95ae6a2948d9ffde64c419845\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f1c5947f95698f6a281d724a14c88cf\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f1c5947f95698f6a281d724a14c88cf\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c176bc85a783f4b95615ae4634f8ae2f\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c176bc85a783f4b95615ae4634f8ae2f\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c4f5c5e1a3cc63e3269d5bb878123ce\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c4f5c5e1a3cc63e3269d5bb878123ce\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d8af47904ad7f9485d00657e6341c9f\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d8af47904ad7f9485d00657e6341c9f\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb90d5889eaa6b542282a5020f72f14d\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb90d5889eaa6b542282a5020f72f14d\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a811e7d3a67ee70560667c9e624d0050\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a811e7d3a67ee70560667c9e624d0050\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f74f887e8617a184c260b963fe8585b\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f74f887e8617a184c260b963fe8585b\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9228a8b7e1895d8104ec3da5ca11dea2\transformed\integrity-1.1.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9228a8b7e1895d8104ec3da5ca11dea2\transformed\integrity-1.1.0\AndroidManifest.xml:5:5-6:19
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91a05db1f70fcc1560e99a1a2e5a1e0b\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91a05db1f70fcc1560e99a1a2e5a1e0b\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2daf4fa1f3706cd82523bfe6089ca9b2\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2daf4fa1f3706cd82523bfe6089ca9b2\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960c7054b802f15a0b75b85e164cffa8\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960c7054b802f15a0b75b85e164cffa8\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f06ba6d15bb51653c2986f9faea87ba3\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f06ba6d15bb51653c2986f9faea87ba3\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a5b100d33267656477d10d382e016a1\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a5b100d33267656477d10d382e016a1\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abae32c1600cfb9e911ec112f910fe8b\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abae32c1600cfb9e911ec112f910fe8b\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92e8c801482673d28198346e411930c4\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92e8c801482673d28198346e411930c4\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4ae6dc4e7b1dfb5b9ce574b1ad21a1c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4ae6dc4e7b1dfb5b9ce574b1ad21a1c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\137bc933d4e1f9a7501d7d0f059fce7a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\137bc933d4e1f9a7501d7d0f059fce7a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.jakewharton.threetenabp:threetenabp:1.4.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0d4291afdc56b4fa9aca61e2d53cf48\transformed\threetenabp-1.4.6\AndroidManifest.xml:7:5-20
MERGED from [com.jakewharton.threetenabp:threetenabp:1.4.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0d4291afdc56b4fa9aca61e2d53cf48\transformed\threetenabp-1.4.6\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960c7054b802f15a0b75b85e164cffa8\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:20:9-35
	android:label
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:18:9-41
	android:fullBackupContent
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:16:9-54
	android:roundIcon
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:19:9-54
	tools:targetApi
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:22:9-29
	android:icon
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:17:9-43
	android:allowBackup
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:14:9-35
	android:theme
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:21:9-51
	android:dataExtractionRules
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:15:9-65
	android:name
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:13:9-43
activity#com.atom.diesnookerapp.MainActivity
ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:23:9-33:20
	android:label
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:26:13-45
	android:exported
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:27:13-55
	android:name
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:24:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:28:13-32:29
action#android.intent.action.MAIN
ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:29:17-69
	android:name
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:29:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:31:17-77
	android:name
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:31:27-74
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:36:9-44:20
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:110:9-121:20
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:38:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:37:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:39:13-43:29
action#android.intent.action.BOOT_COMPLETED
ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:40:17-79
	android:name
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:40:25-76
action#android.intent.action.TIME_SET
ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:41:17-73
	android:name
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:41:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:42:17-81
	android:name
		ADDED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml:42:25-78
uses-sdk
INJECTED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml
INJECTED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8a06c949cfa652a611f2acfa10a87be\transformed\viewbinding-8.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8a06c949cfa652a611f2acfa10a87be\transformed\viewbinding-8.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29b36062e5fe2b6a9996eb16f4854927\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29b36062e5fe2b6a9996eb16f4854927\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7eb0b971608d67ff1f47a47c81bdc2c0\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7eb0b971608d67ff1f47a47c81bdc2c0\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f2b5a48b3fe5567547a5485090d7705\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f2b5a48b3fe5567547a5485090d7705\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e20a0b3acc200aaf9aba33ac523d0336\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e20a0b3acc200aaf9aba33ac523d0336\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b085cf425b5208770b1733a512f3bf9\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b085cf425b5208770b1733a512f3bf9\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85b578f36c5215c705f2b8fa39faf60e\transformed\navigation-fragment-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85b578f36c5215c705f2b8fa39faf60e\transformed\navigation-fragment-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14ba56137749021c9a2be664170d7ac7\transformed\navigation-ui-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14ba56137749021c9a2be664170d7ac7\transformed\navigation-ui-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b064642397aead0c4b677d2bb09b1c2b\transformed\navigation-ui-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b064642397aead0c4b677d2bb09b1c2b\transformed\navigation-ui-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b903733d615c0b2ff05f9b9df539ba65\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b903733d615c0b2ff05f9b9df539ba65\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62593010688224dfc5b372405b080b58\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62593010688224dfc5b372405b080b58\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cec108e13a9d083e64db08e1b6145385\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cec108e13a9d083e64db08e1b6145385\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\edf207558568566ef6df89a74527d120\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\edf207558568566ef6df89a74527d120\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3063590ab377558e02f95f2978ee188b\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3063590ab377558e02f95f2978ee188b\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\545b8561523e2b1f4d0d7838087a8a04\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\545b8561523e2b1f4d0d7838087a8a04\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0c3da69700d8dfa40aae4cd8e12e270\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0c3da69700d8dfa40aae4cd8e12e270\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f0852f393a02041fb6485b4bdfdaba16\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f0852f393a02041fb6485b4bdfdaba16\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f935ad6ff8f4a372e6e4c3926f45186a\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f935ad6ff8f4a372e6e4c3926f45186a\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ddfac4953b40634319c2ee646316b01\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ddfac4953b40634319c2ee646316b01\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\342fc164069752ca474125786d028d25\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\342fc164069752ca474125786d028d25\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3345a7fbe747144276fa9999773cd59e\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3345a7fbe747144276fa9999773cd59e\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb53497875ff465f7931d65e5429b5b8\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb53497875ff465f7931d65e5429b5b8\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d4b7da40d5e42e728aab7fb52f7c68c\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d4b7da40d5e42e728aab7fb52f7c68c\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b415f8502827851acc5588034aaa524f\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b415f8502827851acc5588034aaa524f\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\746f9ea1c7e141b7cd1c54c6bb6bb0fa\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\746f9ea1c7e141b7cd1c54c6bb6bb0fa\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\630a6b64cf0b99c94039eb94430e9039\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\630a6b64cf0b99c94039eb94430e9039\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2965a251595db0b0aea7828df95d2c22\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2965a251595db0b0aea7828df95d2c22\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4d885512c0beef2ec8aea2204c9f1c7\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4d885512c0beef2ec8aea2204c9f1c7\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13b61402139f7407d176cf88d856f408\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13b61402139f7407d176cf88d856f408\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e8c4906aeb6063dad76cbfa2a875519\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e8c4906aeb6063dad76cbfa2a875519\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f80cdb95ae6a2948d9ffde64c419845\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f80cdb95ae6a2948d9ffde64c419845\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1807240b62a7a8a8286290202eecc98f\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1807240b62a7a8a8286290202eecc98f\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f1c5947f95698f6a281d724a14c88cf\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f1c5947f95698f6a281d724a14c88cf\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:21:5-23:64
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfd8ce3ca23c7f1b2cd539ccd756cc0a\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfd8ce3ca23c7f1b2cd539ccd756cc0a\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c176bc85a783f4b95615ae4634f8ae2f\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c176bc85a783f4b95615ae4634f8ae2f\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c4f5c5e1a3cc63e3269d5bb878123ce\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c4f5c5e1a3cc63e3269d5bb878123ce\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d8af47904ad7f9485d00657e6341c9f\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d8af47904ad7f9485d00657e6341c9f\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb90d5889eaa6b542282a5020f72f14d\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb90d5889eaa6b542282a5020f72f14d\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a811e7d3a67ee70560667c9e624d0050\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a811e7d3a67ee70560667c9e624d0050\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39e6ae3b5b803b311daf42021a91c950\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39e6ae3b5b803b311daf42021a91c950\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-auth-api-phone:17.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a24007aeddf495323f15817b62f08e3\transformed\play-services-auth-api-phone-17.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:17.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a24007aeddf495323f15817b62f08e3\transformed\play-services-auth-api-phone-17.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f74f887e8617a184c260b963fe8585b\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f74f887e8617a184c260b963fe8585b\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d710c6c42ffe0a6fb77c1916e456f480\transformed\recaptcha-18.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d710c6c42ffe0a6fb77c1916e456f480\transformed\recaptcha-18.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9228a8b7e1895d8104ec3da5ca11dea2\transformed\integrity-1.1.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9228a8b7e1895d8104ec3da5ca11dea2\transformed\integrity-1.1.0\AndroidManifest.xml:4:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\476165fc5437857fd89974625a791c5a\transformed\work-runtime-ktx-2.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\476165fc5437857fd89974625a791c5a\transformed\work-runtime-ktx-2.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58dea54d1ca4f9d9a165d72ef873c4cc\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58dea54d1ca4f9d9a165d72ef873c4cc\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3477c63391d771930e606dbf0a8d42a5\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3477c63391d771930e606dbf0a8d42a5\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71de2404ef70d4038d75f48e1378fd1f\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71de2404ef70d4038d75f48e1378fd1f\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2cc96fae3303c9b9ee2713056a6ecbd\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2cc96fae3303c9b9ee2713056a6ecbd\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbe01b0c014df9a20d7ee5e1186f3b22\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbe01b0c014df9a20d7ee5e1186f3b22\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\203bebdaf46ee5720eee11b108418f1d\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\203bebdaf46ee5720eee11b108418f1d\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1078977e84cf2f81e9b64ac9497bef28\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1078977e84cf2f81e9b64ac9497bef28\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d4b274c36eaeab0b07fee59e023b9ae\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d4b274c36eaeab0b07fee59e023b9ae\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3cb10dbac82f1f5fa7d5347ce82a4e1\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3cb10dbac82f1f5fa7d5347ce82a4e1\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2e672e89faeb26611b2eb615ced0995\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2e672e89faeb26611b2eb615ced0995\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e188628e5386314399d09afe6f3689d\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e188628e5386314399d09afe6f3689d\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19a97e95153f937a5eea3530d904e77a\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19a97e95153f937a5eea3530d904e77a\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71ed0c9230eab0bc603dcc68cfab7b18\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71ed0c9230eab0bc603dcc68cfab7b18\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91a05db1f70fcc1560e99a1a2e5a1e0b\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91a05db1f70fcc1560e99a1a2e5a1e0b\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5463204cc3b6345dbf5562ab6d7cc40d\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5463204cc3b6345dbf5562ab6d7cc40d\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c2724dca3c98e276efaab131df51d3a\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c2724dca3c98e276efaab131df51d3a\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2daf4fa1f3706cd82523bfe6089ca9b2\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2daf4fa1f3706cd82523bfe6089ca9b2\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960c7054b802f15a0b75b85e164cffa8\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960c7054b802f15a0b75b85e164cffa8\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acfd5b96537540834722577deee275fe\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acfd5b96537540834722577deee275fe\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39d3ae8d4a330c67607b288e5601c24e\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39d3ae8d4a330c67607b288e5601c24e\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f06ba6d15bb51653c2986f9faea87ba3\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f06ba6d15bb51653c2986f9faea87ba3\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\323fccee1568262fa96249e1496cc48f\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\323fccee1568262fa96249e1496cc48f\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2922db0595fb1e5606cfbb7d37671928\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2922db0595fb1e5606cfbb7d37671928\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\907836426769673d6f72fd6688ee3b88\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\907836426769673d6f72fd6688ee3b88\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b1fbaf21c106f939b47d187adb1c0fac\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b1fbaf21c106f939b47d187adb1c0fac\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67e1c84be8318a5402cb5635d76c8e98\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67e1c84be8318a5402cb5635d76c8e98\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72cd04d57cf61f97c873831b15834ca5\transformed\lifecycle-service-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72cd04d57cf61f97c873831b15834ca5\transformed\lifecycle-service-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\095d12f777f8d88c9b4836652b403fa4\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\095d12f777f8d88c9b4836652b403fa4\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\220e66dfbaf79c535786699abfffffd8\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\220e66dfbaf79c535786699abfffffd8\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62fbe1e14622130bc6e17cd2544681b7\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62fbe1e14622130bc6e17cd2544681b7\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61951ba496fe13479e7e43963c62b9cb\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61951ba496fe13479e7e43963c62b9cb\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91aefd5272af96834250923d7b3d532b\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91aefd5272af96834250923d7b3d532b\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c6802c9b562899c207089fd57e4a1fc\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c6802c9b562899c207089fd57e4a1fc\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a5b100d33267656477d10d382e016a1\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a5b100d33267656477d10d382e016a1\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4e6d9afd0ba598fc2f0f129d06b1e7b\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4e6d9afd0ba598fc2f0f129d06b1e7b\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abae32c1600cfb9e911ec112f910fe8b\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abae32c1600cfb9e911ec112f910fe8b\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92e8c801482673d28198346e411930c4\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92e8c801482673d28198346e411930c4\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6254bcc2d81321e92f6d0c196b1d48d\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6254bcc2d81321e92f6d0c196b1d48d\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c3f9c036a4b978d45aa46e441d17c9d\transformed\activity-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c3f9c036a4b978d45aa46e441d17c9d\transformed\activity-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d844f6e44315539624fbb7ebb020ef4d\transformed\activity-ktx-1.10.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d844f6e44315539624fbb7ebb020ef4d\transformed\activity-ktx-1.10.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92450d02dfb2767d0073e6e1885d63e5\transformed\activity-compose-1.10.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92450d02dfb2767d0073e6e1885d63e5\transformed\activity-compose-1.10.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e380358256c1e9482b3d8f26b625fdd2\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e380358256c1e9482b3d8f26b625fdd2\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\755be06f9b0e6e8f28aaf6e4fbac3477\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\755be06f9b0e6e8f28aaf6e4fbac3477\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f89e2b54c583c2b631f13d7a4065b5b\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f89e2b54c583c2b631f13d7a4065b5b\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\082660c5f01ce2062ab5bf888cdcb292\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\082660c5f01ce2062ab5bf888cdcb292\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a75f9814effd09ab0114ecc4773eaaa3\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a75f9814effd09ab0114ecc4773eaaa3\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\075c5acae7e5deae36206802f62148b0\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\075c5acae7e5deae36206802f62148b0\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6139beaedbf45a3dbb9e7c49f83721e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6139beaedbf45a3dbb9e7c49f83721e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4ae6dc4e7b1dfb5b9ce574b1ad21a1c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4ae6dc4e7b1dfb5b9ce574b1ad21a1c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a56bf67678845e6b8d8185b57b0bcaff\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a56bf67678845e6b8d8185b57b0bcaff\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3615514103634996edc945592e2d2f6c\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3615514103634996edc945592e2d2f6c\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\137bc933d4e1f9a7501d7d0f059fce7a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\137bc933d4e1f9a7501d7d0f059fce7a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d4690ed394e54ec95578d84de51d293\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d4690ed394e54ec95578d84de51d293\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83dd44f1faea239aa84074537b53dae5\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83dd44f1faea239aa84074537b53dae5\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb23ff2ed1153403db45fdc7c6a07b36\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb23ff2ed1153403db45fdc7c6a07b36\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08800e8822abfbb6c6677901ff35004e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08800e8822abfbb6c6677901ff35004e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a08d0afea0fb8d04257f17bda2a4020b\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a08d0afea0fb8d04257f17bda2a4020b\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adc8c372d1b4d5985567fdce3c1ff1a0\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adc8c372d1b4d5985567fdce3c1ff1a0\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0720eb107e89c923d3bf183f803b709a\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0720eb107e89c923d3bf183f803b709a\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3805e241e78cd74e1da024359e9cf97\transformed\grpc-android-1.52.1\AndroidManifest.xml:5:5-7:41
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3805e241e78cd74e1da024359e9cf97\transformed\grpc-android-1.52.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.jakewharton.threetenabp:threetenabp:1.4.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0d4291afdc56b4fa9aca61e2d53cf48\transformed\threetenabp-1.4.6\AndroidManifest.xml:5:5-44
MERGED from [com.jakewharton.threetenabp:threetenabp:1.4.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0d4291afdc56b4fa9aca61e2d53cf48\transformed\threetenabp-1.4.6\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba3fcf8cb7800215380828f3c58fc8c\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba3fcf8cb7800215380828f3c58fc8c\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:23:9-61
	android:targetSdkVersion
		INJECTED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Home\Dokumente\AndroidStudioProjects\DieSnookerAppGitHub\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4d885512c0beef2ec8aea2204c9f1c7\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4d885512c0beef2ec8aea2204c9f1c7\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4d885512c0beef2ec8aea2204c9f1c7\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f80cdb95ae6a2948d9ffde64c419845\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f80cdb95ae6a2948d9ffde64c419845\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f80cdb95ae6a2948d9ffde64c419845\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:24:13-63
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f1c5947f95698f6a281d724a14c88cf\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c176bc85a783f4b95615ae4634f8ae2f\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c176bc85a783f4b95615ae4634f8ae2f\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c4f5c5e1a3cc63e3269d5bb878123ce\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c4f5c5e1a3cc63e3269d5bb878123ce\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d8af47904ad7f9485d00657e6341c9f\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d8af47904ad7f9485d00657e6341c9f\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f1c5947f95698f6a281d724a14c88cf\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:10:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f1c5947f95698f6a281d724a14c88cf\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:9:13-84
meta-data#com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar
ADDED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f1c5947f95698f6a281d724a14c88cf\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f1c5947f95698f6a281d724a14c88cf\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f1c5947f95698f6a281d724a14c88cf\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:12:17-119
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c4f5c5e1a3cc63e3269d5bb878123ce\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c4f5c5e1a3cc63e3269d5bb878123ce\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d710c6c42ffe0a6fb77c1916e456f480\transformed\recaptcha-18.1.2\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d710c6c42ffe0a6fb77c1916e456f480\transformed\recaptcha-18.1.2\AndroidManifest.xml:8:5-79
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:24:5-79
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3805e241e78cd74e1da024359e9cf97\transformed\grpc-android-1.52.1\AndroidManifest.xml:9:5-79
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3805e241e78cd74e1da024359e9cf97\transformed\grpc-android-1.52.1\AndroidManifest.xml:9:5-79
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:26:22-76
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:27-75
data
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:41:17-44:51
	android:path
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:43:21-37
	android:host
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:42:21-49
	android:scheme
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:44:21-48
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2ad9fa2ed5a899360c039ae1804e2da\transformed\firebase-auth-22.3.0\AndroidManifest.xml:70:17-109
meta-data#com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar
ADDED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c176bc85a783f4b95615ae4634f8ae2f\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c176bc85a783f4b95615ae4634f8ae2f\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c176bc85a783f4b95615ae4634f8ae2f\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:13:17-129
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c4f5c5e1a3cc63e3269d5bb878123ce\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c4f5c5e1a3cc63e3269d5bb878123ce\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c4f5c5e1a3cc63e3269d5bb878123ce\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c4f5c5e1a3cc63e3269d5bb878123ce\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c4f5c5e1a3cc63e3269d5bb878123ce\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c4f5c5e1a3cc63e3269d5bb878123ce\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:21:17-111
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d8af47904ad7f9485d00657e6341c9f\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d8af47904ad7f9485d00657e6341c9f\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d8af47904ad7f9485d00657e6341c9f\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f74f887e8617a184c260b963fe8585b\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f74f887e8617a184c260b963fe8585b\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f74f887e8617a184c260b963fe8585b\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f74f887e8617a184c260b963fe8585b\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
uses-permission#android.permission.WAKE_LOCK
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:23:5-68
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:23:22-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:26:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:26:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2daf4fa1f3706cd82523bfe6089ca9b2\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2daf4fa1f3706cd82523bfe6089ca9b2\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f06ba6d15bb51653c2986f9faea87ba3\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f06ba6d15bb51653c2986f9faea87ba3\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4ae6dc4e7b1dfb5b9ce574b1ad21a1c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4ae6dc4e7b1dfb5b9ce574b1ad21a1c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\AndroidManifest.xml:140:25-85
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91a05db1f70fcc1560e99a1a2e5a1e0b\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91a05db1f70fcc1560e99a1a2e5a1e0b\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91a05db1f70fcc1560e99a1a2e5a1e0b\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91a05db1f70fcc1560e99a1a2e5a1e0b\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91a05db1f70fcc1560e99a1a2e5a1e0b\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91a05db1f70fcc1560e99a1a2e5a1e0b\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2daf4fa1f3706cd82523bfe6089ca9b2\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2daf4fa1f3706cd82523bfe6089ca9b2\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2daf4fa1f3706cd82523bfe6089ca9b2\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960c7054b802f15a0b75b85e164cffa8\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960c7054b802f15a0b75b85e164cffa8\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960c7054b802f15a0b75b85e164cffa8\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
permission#com.atom.diesnookerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960c7054b802f15a0b75b85e164cffa8\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960c7054b802f15a0b75b85e164cffa8\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960c7054b802f15a0b75b85e164cffa8\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960c7054b802f15a0b75b85e164cffa8\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960c7054b802f15a0b75b85e164cffa8\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
uses-permission#com.atom.diesnookerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960c7054b802f15a0b75b85e164cffa8\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960c7054b802f15a0b75b85e164cffa8\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f06ba6d15bb51653c2986f9faea87ba3\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f06ba6d15bb51653c2986f9faea87ba3\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f06ba6d15bb51653c2986f9faea87ba3\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a5b100d33267656477d10d382e016a1\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a5b100d33267656477d10d382e016a1\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a5b100d33267656477d10d382e016a1\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a5b100d33267656477d10d382e016a1\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a5b100d33267656477d10d382e016a1\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92e8c801482673d28198346e411930c4\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92e8c801482673d28198346e411930c4\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92e8c801482673d28198346e411930c4\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
