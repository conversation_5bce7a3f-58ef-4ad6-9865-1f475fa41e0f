// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentExerciseDetailSplitsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton addScore10Button;

  @NonNull
  public final MaterialButton addScore3Button;

  @NonNull
  public final MaterialButton addScore6Button;

  @NonNull
  public final MaterialButton addTimeButton;

  @NonNull
  public final TextView scores10Text;

  @NonNull
  public final TextView scores3Text;

  @NonNull
  public final TextView scores6Text;

  @NonNull
  public final MaterialButton showGraphButton;

  @NonNull
  public final TextView titleText;

  private FragmentExerciseDetailSplitsBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton addScore10Button, @NonNull MaterialButton addScore3Button,
      @NonNull MaterialButton addScore6Button, @NonNull MaterialButton addTimeButton,
      @NonNull TextView scores10Text, @NonNull TextView scores3Text, @NonNull TextView scores6Text,
      @NonNull MaterialButton showGraphButton, @NonNull TextView titleText) {
    this.rootView = rootView;
    this.addScore10Button = addScore10Button;
    this.addScore3Button = addScore3Button;
    this.addScore6Button = addScore6Button;
    this.addTimeButton = addTimeButton;
    this.scores10Text = scores10Text;
    this.scores3Text = scores3Text;
    this.scores6Text = scores6Text;
    this.showGraphButton = showGraphButton;
    this.titleText = titleText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentExerciseDetailSplitsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentExerciseDetailSplitsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_exercise_detail_splits, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentExerciseDetailSplitsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addScore10Button;
      MaterialButton addScore10Button = ViewBindings.findChildViewById(rootView, id);
      if (addScore10Button == null) {
        break missingId;
      }

      id = R.id.addScore3Button;
      MaterialButton addScore3Button = ViewBindings.findChildViewById(rootView, id);
      if (addScore3Button == null) {
        break missingId;
      }

      id = R.id.addScore6Button;
      MaterialButton addScore6Button = ViewBindings.findChildViewById(rootView, id);
      if (addScore6Button == null) {
        break missingId;
      }

      id = R.id.addTimeButton;
      MaterialButton addTimeButton = ViewBindings.findChildViewById(rootView, id);
      if (addTimeButton == null) {
        break missingId;
      }

      id = R.id.scores10Text;
      TextView scores10Text = ViewBindings.findChildViewById(rootView, id);
      if (scores10Text == null) {
        break missingId;
      }

      id = R.id.scores3Text;
      TextView scores3Text = ViewBindings.findChildViewById(rootView, id);
      if (scores3Text == null) {
        break missingId;
      }

      id = R.id.scores6Text;
      TextView scores6Text = ViewBindings.findChildViewById(rootView, id);
      if (scores6Text == null) {
        break missingId;
      }

      id = R.id.showGraphButton;
      MaterialButton showGraphButton = ViewBindings.findChildViewById(rootView, id);
      if (showGraphButton == null) {
        break missingId;
      }

      id = R.id.titleText;
      TextView titleText = ViewBindings.findChildViewById(rootView, id);
      if (titleText == null) {
        break missingId;
      }

      return new FragmentExerciseDetailSplitsBinding((LinearLayout) rootView, addScore10Button,
          addScore3Button, addScore6Button, addTimeButton, scores10Text, scores3Text, scores6Text,
          showGraphButton, titleText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
