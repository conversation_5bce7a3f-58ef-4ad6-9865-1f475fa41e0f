// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.CheckBox;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentUserManagementBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialCardView addConnectionCard;

  @NonNull
  public final MaterialButton addTrainerButton;

  @NonNull
  public final TextView connectionsLabel;

  @NonNull
  public final RecyclerView connectionsRecyclerView;

  @NonNull
  public final TextInputEditText emailEditText;

  @NonNull
  public final TextInputLayout emailInputLayout;

  @NonNull
  public final TextView emptyStateTextView;

  @NonNull
  public final CheckBox exerciseAccessCheckbox;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final CheckBox selfAssessmentAccessCheckbox;

  @NonNull
  public final TextView titleTextView;

  @NonNull
  public final AutoCompleteTextView trainerTypeDropdown;

  @NonNull
  public final TextInputLayout trainerTypeInputLayout;

  private FragmentUserManagementBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialCardView addConnectionCard, @NonNull MaterialButton addTrainerButton,
      @NonNull TextView connectionsLabel, @NonNull RecyclerView connectionsRecyclerView,
      @NonNull TextInputEditText emailEditText, @NonNull TextInputLayout emailInputLayout,
      @NonNull TextView emptyStateTextView, @NonNull CheckBox exerciseAccessCheckbox,
      @NonNull ProgressBar progressBar, @NonNull CheckBox selfAssessmentAccessCheckbox,
      @NonNull TextView titleTextView, @NonNull AutoCompleteTextView trainerTypeDropdown,
      @NonNull TextInputLayout trainerTypeInputLayout) {
    this.rootView = rootView;
    this.addConnectionCard = addConnectionCard;
    this.addTrainerButton = addTrainerButton;
    this.connectionsLabel = connectionsLabel;
    this.connectionsRecyclerView = connectionsRecyclerView;
    this.emailEditText = emailEditText;
    this.emailInputLayout = emailInputLayout;
    this.emptyStateTextView = emptyStateTextView;
    this.exerciseAccessCheckbox = exerciseAccessCheckbox;
    this.progressBar = progressBar;
    this.selfAssessmentAccessCheckbox = selfAssessmentAccessCheckbox;
    this.titleTextView = titleTextView;
    this.trainerTypeDropdown = trainerTypeDropdown;
    this.trainerTypeInputLayout = trainerTypeInputLayout;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentUserManagementBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentUserManagementBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_user_management, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentUserManagementBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addConnectionCard;
      MaterialCardView addConnectionCard = ViewBindings.findChildViewById(rootView, id);
      if (addConnectionCard == null) {
        break missingId;
      }

      id = R.id.addTrainerButton;
      MaterialButton addTrainerButton = ViewBindings.findChildViewById(rootView, id);
      if (addTrainerButton == null) {
        break missingId;
      }

      id = R.id.connectionsLabel;
      TextView connectionsLabel = ViewBindings.findChildViewById(rootView, id);
      if (connectionsLabel == null) {
        break missingId;
      }

      id = R.id.connectionsRecyclerView;
      RecyclerView connectionsRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (connectionsRecyclerView == null) {
        break missingId;
      }

      id = R.id.emailEditText;
      TextInputEditText emailEditText = ViewBindings.findChildViewById(rootView, id);
      if (emailEditText == null) {
        break missingId;
      }

      id = R.id.emailInputLayout;
      TextInputLayout emailInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (emailInputLayout == null) {
        break missingId;
      }

      id = R.id.emptyStateTextView;
      TextView emptyStateTextView = ViewBindings.findChildViewById(rootView, id);
      if (emptyStateTextView == null) {
        break missingId;
      }

      id = R.id.exerciseAccessCheckbox;
      CheckBox exerciseAccessCheckbox = ViewBindings.findChildViewById(rootView, id);
      if (exerciseAccessCheckbox == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.selfAssessmentAccessCheckbox;
      CheckBox selfAssessmentAccessCheckbox = ViewBindings.findChildViewById(rootView, id);
      if (selfAssessmentAccessCheckbox == null) {
        break missingId;
      }

      id = R.id.titleTextView;
      TextView titleTextView = ViewBindings.findChildViewById(rootView, id);
      if (titleTextView == null) {
        break missingId;
      }

      id = R.id.trainerTypeDropdown;
      AutoCompleteTextView trainerTypeDropdown = ViewBindings.findChildViewById(rootView, id);
      if (trainerTypeDropdown == null) {
        break missingId;
      }

      id = R.id.trainerTypeInputLayout;
      TextInputLayout trainerTypeInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (trainerTypeInputLayout == null) {
        break missingId;
      }

      return new FragmentUserManagementBinding((ConstraintLayout) rootView, addConnectionCard,
          addTrainerButton, connectionsLabel, connectionsRecyclerView, emailEditText,
          emailInputLayout, emptyStateTextView, exerciseAccessCheckbox, progressBar,
          selfAssessmentAccessCheckbox, titleTextView, trainerTypeDropdown, trainerTypeInputLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
