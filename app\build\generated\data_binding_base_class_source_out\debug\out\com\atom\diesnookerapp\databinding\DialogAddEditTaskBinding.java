// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAddEditTaskBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton cancelButton;

  @NonNull
  public final TextInputEditText newCategoryNameEditText;

  @NonNull
  public final TextInputLayout newCategoryNameInputLayout;

  @NonNull
  public final MaterialButton saveButton;

  @NonNull
  public final AutoCompleteTextView taskCategoryAutoCompleteTextView;

  @NonNull
  public final TextInputLayout taskCategoryInputLayout;

  @NonNull
  public final TextInputEditText taskDescriptionEditText;

  @NonNull
  public final TextInputLayout taskDescriptionInputLayout;

  @NonNull
  public final AutoCompleteTextView taskFrequencyAutoCompleteTextView;

  @NonNull
  public final TextInputLayout taskFrequencyInputLayout;

  @NonNull
  public final TextInputEditText taskPointsEditText;

  @NonNull
  public final TextInputLayout taskPointsInputLayout;

  @NonNull
  public final TextInputEditText taskTitleEditText;

  @NonNull
  public final TextInputLayout taskTitleInputLayout;

  @NonNull
  public final TextInputEditText taskWeeklyFrequencyCountEditText;

  @NonNull
  public final TextInputLayout taskWeeklyFrequencyCountLayout;

  private DialogAddEditTaskBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton cancelButton, @NonNull TextInputEditText newCategoryNameEditText,
      @NonNull TextInputLayout newCategoryNameInputLayout, @NonNull MaterialButton saveButton,
      @NonNull AutoCompleteTextView taskCategoryAutoCompleteTextView,
      @NonNull TextInputLayout taskCategoryInputLayout,
      @NonNull TextInputEditText taskDescriptionEditText,
      @NonNull TextInputLayout taskDescriptionInputLayout,
      @NonNull AutoCompleteTextView taskFrequencyAutoCompleteTextView,
      @NonNull TextInputLayout taskFrequencyInputLayout,
      @NonNull TextInputEditText taskPointsEditText, @NonNull TextInputLayout taskPointsInputLayout,
      @NonNull TextInputEditText taskTitleEditText, @NonNull TextInputLayout taskTitleInputLayout,
      @NonNull TextInputEditText taskWeeklyFrequencyCountEditText,
      @NonNull TextInputLayout taskWeeklyFrequencyCountLayout) {
    this.rootView = rootView;
    this.cancelButton = cancelButton;
    this.newCategoryNameEditText = newCategoryNameEditText;
    this.newCategoryNameInputLayout = newCategoryNameInputLayout;
    this.saveButton = saveButton;
    this.taskCategoryAutoCompleteTextView = taskCategoryAutoCompleteTextView;
    this.taskCategoryInputLayout = taskCategoryInputLayout;
    this.taskDescriptionEditText = taskDescriptionEditText;
    this.taskDescriptionInputLayout = taskDescriptionInputLayout;
    this.taskFrequencyAutoCompleteTextView = taskFrequencyAutoCompleteTextView;
    this.taskFrequencyInputLayout = taskFrequencyInputLayout;
    this.taskPointsEditText = taskPointsEditText;
    this.taskPointsInputLayout = taskPointsInputLayout;
    this.taskTitleEditText = taskTitleEditText;
    this.taskTitleInputLayout = taskTitleInputLayout;
    this.taskWeeklyFrequencyCountEditText = taskWeeklyFrequencyCountEditText;
    this.taskWeeklyFrequencyCountLayout = taskWeeklyFrequencyCountLayout;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAddEditTaskBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAddEditTaskBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_add_edit_task, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAddEditTaskBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cancelButton;
      MaterialButton cancelButton = ViewBindings.findChildViewById(rootView, id);
      if (cancelButton == null) {
        break missingId;
      }

      id = R.id.newCategoryNameEditText;
      TextInputEditText newCategoryNameEditText = ViewBindings.findChildViewById(rootView, id);
      if (newCategoryNameEditText == null) {
        break missingId;
      }

      id = R.id.newCategoryNameInputLayout;
      TextInputLayout newCategoryNameInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (newCategoryNameInputLayout == null) {
        break missingId;
      }

      id = R.id.saveButton;
      MaterialButton saveButton = ViewBindings.findChildViewById(rootView, id);
      if (saveButton == null) {
        break missingId;
      }

      id = R.id.taskCategoryAutoCompleteTextView;
      AutoCompleteTextView taskCategoryAutoCompleteTextView = ViewBindings.findChildViewById(rootView, id);
      if (taskCategoryAutoCompleteTextView == null) {
        break missingId;
      }

      id = R.id.taskCategoryInputLayout;
      TextInputLayout taskCategoryInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskCategoryInputLayout == null) {
        break missingId;
      }

      id = R.id.taskDescriptionEditText;
      TextInputEditText taskDescriptionEditText = ViewBindings.findChildViewById(rootView, id);
      if (taskDescriptionEditText == null) {
        break missingId;
      }

      id = R.id.taskDescriptionInputLayout;
      TextInputLayout taskDescriptionInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskDescriptionInputLayout == null) {
        break missingId;
      }

      id = R.id.taskFrequencyAutoCompleteTextView;
      AutoCompleteTextView taskFrequencyAutoCompleteTextView = ViewBindings.findChildViewById(rootView, id);
      if (taskFrequencyAutoCompleteTextView == null) {
        break missingId;
      }

      id = R.id.taskFrequencyInputLayout;
      TextInputLayout taskFrequencyInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskFrequencyInputLayout == null) {
        break missingId;
      }

      id = R.id.taskPointsEditText;
      TextInputEditText taskPointsEditText = ViewBindings.findChildViewById(rootView, id);
      if (taskPointsEditText == null) {
        break missingId;
      }

      id = R.id.taskPointsInputLayout;
      TextInputLayout taskPointsInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskPointsInputLayout == null) {
        break missingId;
      }

      id = R.id.taskTitleEditText;
      TextInputEditText taskTitleEditText = ViewBindings.findChildViewById(rootView, id);
      if (taskTitleEditText == null) {
        break missingId;
      }

      id = R.id.taskTitleInputLayout;
      TextInputLayout taskTitleInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskTitleInputLayout == null) {
        break missingId;
      }

      id = R.id.taskWeeklyFrequencyCountEditText;
      TextInputEditText taskWeeklyFrequencyCountEditText = ViewBindings.findChildViewById(rootView, id);
      if (taskWeeklyFrequencyCountEditText == null) {
        break missingId;
      }

      id = R.id.taskWeeklyFrequencyCountLayout;
      TextInputLayout taskWeeklyFrequencyCountLayout = ViewBindings.findChildViewById(rootView, id);
      if (taskWeeklyFrequencyCountLayout == null) {
        break missingId;
      }

      return new DialogAddEditTaskBinding((LinearLayout) rootView, cancelButton,
          newCategoryNameEditText, newCategoryNameInputLayout, saveButton,
          taskCategoryAutoCompleteTextView, taskCategoryInputLayout, taskDescriptionEditText,
          taskDescriptionInputLayout, taskFrequencyAutoCompleteTextView, taskFrequencyInputLayout,
          taskPointsEditText, taskPointsInputLayout, taskTitleEditText, taskTitleInputLayout,
          taskWeeklyFrequencyCountEditText, taskWeeklyFrequencyCountLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
